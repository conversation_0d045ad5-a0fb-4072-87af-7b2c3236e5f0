<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oscilloscopio Audio 2D - Effetto CRT</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            font-family: 'Courier New', monospace;
            color: #00ff00;
            overflow: hidden;
        }

        .crt-container {
            position: relative;
            width: 800px;
            height: 600px;
            margin: 0 auto;
            background: #000;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 
                0 0 50px rgba(0, 255, 0, 0.3),
                inset 0 0 100px rgba(0, 50, 0, 0.5);
        }

        .screen {
            position: relative;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, #001100 0%, #000000 70%);
            border: 3px solid #333;
            overflow: hidden;
        }

        canvas {
            width: 100%;
            height: 100%;
            display: block;
            filter: 
                brightness(1.2)
                contrast(1.1)
                drop-shadow(0 0 10px #00ff00);
        }

        .scanlines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            background: linear-gradient(
                transparent 50%,
                rgba(0, 255, 0, 0.03) 50%
            );
            background-size: 100% 4px;
            animation: scanlines 0.1s linear infinite;
        }

        .crt-flicker {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            background: rgba(0, 255, 0, 0.02);
            animation: flicker 0.15s infinite alternate;
        }

        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 120px;
            height: 4px;
            background: #003300;
            outline: none;
            border-radius: 2px;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background: #00ff00;
            cursor: pointer;
            border-radius: 50%;
            box-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
        }

        input[type="range"]::-moz-range-thumb {
            width: 16px;
            height: 16px;
            background: #00ff00;
            cursor: pointer;
            border-radius: 50%;
            border: none;
            box-shadow: 0 0 8px rgba(0, 255, 0, 0.5);
        }

        .slider-label {
            color: #00aa00;
            font-size: 11px;
            min-width: 80px;
        }

        button {
            background: #001100;
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 8px 15px;
            margin: 5px;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            transition: all 0.2s;
        }

        button:hover {
            background: #00ff00;
            color: #000;
            box-shadow: 0 0 10px #00ff00;
        }

        .info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            font-size: 12px;
            color: #00aa00;
        }

        @keyframes scanlines {
            0% { transform: translateY(0); }
            100% { transform: translateY(4px); }
        }

        @keyframes flicker {
            0% { opacity: 1; }
            100% { opacity: 0.98; }
        }

        .phosphor-trail {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            mix-blend-mode: screen;
        }
    </style>
</head>
<body>
    <div class="crt-container">
        <div class="controls">
            <div class="control-group">
                <button id="stopBtn">Stop</button>
                <input type="file" id="audioFile" accept="audio/*" style="display: none;">
                <button id="loadFile">Carica File Audio</button>
            </div>
            <div class="slider-container">
                <span class="slider-label">Fluorescenza:</span>
                <input type="range" id="phosphorSlider" min="0" max="100" value="50">
                <span id="phosphorValue" class="slider-label">50%</span>
            </div>
            <div class="slider-container">
                <span class="slider-label">Campionamento:</span>
                <input type="range" id="samplingSlider" min="1" max="8" value="1">
                <span id="samplingValue" class="slider-label">Massima</span>
            </div>
        </div>
        
        <div class="screen">
            <canvas id="oscilloscope" width="800" height="600"></canvas>
            <div class="scanlines"></div>
            <div class="crt-flicker"></div>
            <div class="phosphor-trail"></div>
        </div>
        
        <div class="info">
            X: Canale Sinistro | Y: Canale Destro | Oscilloscopio 2D Analogico
        </div>
    </div>

    <script>
        class AudioOscilloscope {
            constructor() {
                this.canvas = document.getElementById('oscilloscope');
                this.ctx = this.canvas.getContext('2d');
                this.audioContext = null;
                this.analyser = null;
                this.splitter = null;
                this.leftAnalyser = null;
                this.rightAnalyser = null;
                this.dataArrayLeft = null;
                this.dataArrayRight = null;
                this.animationId = null;
                this.phosphorLevel = 0.5; // Livello di fluorescenza (0-1)
                this.samplingStep = 1; // Passo di campionamento (1=massima, 8=minima)
                
                this.setupCanvas();
                this.setupEventListeners();
                
                // Phosphor persistence effect
                this.trailCanvas = document.createElement('canvas');
                this.trailCanvas.width = this.canvas.width;
                this.trailCanvas.height = this.canvas.height;
                this.trailCtx = this.trailCanvas.getContext('2d');
            }

            setupCanvas() {
                const rect = this.canvas.getBoundingClientRect();
                this.canvas.width = rect.width * window.devicePixelRatio;
                this.canvas.height = rect.height * window.devicePixelRatio;
                this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
                
                this.centerX = this.canvas.width / (2 * window.devicePixelRatio);
                this.centerY = this.canvas.height / (2 * window.devicePixelRatio);
                this.scaleX = this.centerX * 0.8;
                this.scaleY = this.centerY * 0.8;
            }

            setupEventListeners() {
                document.getElementById('stopBtn').addEventListener('click', () => this.stop());
                document.getElementById('loadFile').addEventListener('click', () => {
                    document.getElementById('audioFile').click();
                });
                document.getElementById('audioFile').addEventListener('change', (e) => this.loadAudioFile(e));
                
                // Slider per la fluorescenza
                const phosphorSlider = document.getElementById('phosphorSlider');
                const phosphorValue = document.getElementById('phosphorValue');
                phosphorSlider.addEventListener('input', (e) => {
                    this.phosphorLevel = e.target.value / 100;
                    phosphorValue.textContent = e.target.value + '%';
                });

                // Slider per il campionamento
                const samplingSlider = document.getElementById('samplingSlider');
                const samplingValue = document.getElementById('samplingValue');
                const samplingLabels = ['Massima', 'Molto alta', 'Alta', 'Media-alta', 'Media', 'Media-bassa', 'Bassa', 'Minima'];
                
                // Imposta il campionamento predefinito a "Massima" (valore 1)
                this.samplingStep = 1;
                samplingValue.textContent = 'Massima';
                
                samplingSlider.addEventListener('input', (e) => {
                    this.samplingStep = parseInt(e.target.value);
                    samplingValue.textContent = samplingLabels[this.samplingStep - 1];
                });
            }


            async loadAudioFile(event) {
                const file = event.target.files[0];
                if (!file) return;

                try {
                    this.stop(); // Ferma qualsiasi riproduzione precedente
                    
                    const arrayBuffer = await file.arrayBuffer();
                    
                    if (!this.audioContext) {
                        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    }
                    
                    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
                    this.source = this.audioContext.createBufferSource();
                    this.source.buffer = audioBuffer;
                    this.source.loop = true;
                    
                    // Crea un gain node per il controllo del volume
                    this.gainNode = this.audioContext.createGain();
                    this.gainNode.gain.value = 0.5;
                    
                    this.setupAnalysers();
                    
                    // Connetti: source -> splitter -> analysers
                    //           source -> gain -> destination (per l'audio)
                    this.source.connect(this.splitter);
                    this.source.connect(this.gainNode);
                    this.gainNode.connect(this.audioContext.destination);
                    
                    this.source.start();
                    this.startVisualization();
                    
                    console.log('File audio caricato e riproduzione avviata');
                } catch (error) {
                    console.error('Errore caricamento file:', error);
                    alert('Errore nel caricamento del file audio.');
                }
            }


            setupAnalysers() {
                // Splitter per separare i canali
                this.splitter = this.audioContext.createChannelSplitter(2);
                
                // Analizzatori per ciascun canale
                this.leftAnalyser = this.audioContext.createAnalyser();
                this.rightAnalyser = this.audioContext.createAnalyser();
                
                this.leftAnalyser.fftSize = 32768;
                this.rightAnalyser.fftSize = 32768;
                
                this.leftAnalyser.smoothingTimeConstant = 0.0;
                this.rightAnalyser.smoothingTimeConstant = 0.0;
                
                // Connessioni
                this.splitter.connect(this.leftAnalyser, 0);
                this.splitter.connect(this.rightAnalyser, 1);
                
                // Array per i dati
                this.dataArrayLeft = new Uint8Array(this.leftAnalyser.frequencyBinCount);
                this.dataArrayRight = new Uint8Array(this.rightAnalyser.frequencyBinCount);
            }

            startVisualization() {
                const draw = () => {
                    this.animationId = requestAnimationFrame(draw);
                    this.drawOscilloscope();
                };
                draw();
            }

            drawOscilloscope() {
                if (!this.leftAnalyser || !this.rightAnalyser) return;

                // Ottieni dati waveform
                this.leftAnalyser.getByteTimeDomainData(this.dataArrayLeft);
                this.rightAnalyser.getByteTimeDomainData(this.dataArrayRight);

                // Effetto phosphor - fade del trail precedente (controllato da slider)
                const fadeAmount = 0.02 + (this.phosphorLevel * 0.08); // Da 0.02 a 0.10
                this.trailCtx.fillStyle = `rgba(0, 0, 0, ${1 - this.phosphorLevel * 0.95})`;
                this.trailCtx.fillRect(0, 0, this.trailCanvas.width, this.trailCanvas.height);

                // Clear main canvas
                this.ctx.fillStyle = 'rgba(0, 17, 0, 0.1)';
                this.ctx.fillRect(0, 0, this.canvas.width / window.devicePixelRatio, this.canvas.height / window.devicePixelRatio);

                // Disegna il trail (effetto phosphor)
                this.ctx.drawImage(this.trailCanvas, 0, 0, this.canvas.width / window.devicePixelRatio, this.canvas.height / window.devicePixelRatio);

                // Disegna gli assi
                this.drawAxes();

                // Disegna l'oscilloscopio 2D con intensità basata sulla fluorescenza
                const brightness = 0.5 + (this.phosphorLevel * 0.5); // Da 0.5 a 1.0
                const glowIntensity = this.phosphorLevel * 10; // Da 0 a 10
                
                this.ctx.strokeStyle = `rgba(0, ${Math.floor(255 * brightness)}, 0, ${brightness})`;
                this.ctx.lineWidth = 1 + this.phosphorLevel;
                this.ctx.shadowColor = '#00ff00';
                this.ctx.shadowBlur = glowIntensity;
                
                this.ctx.beginPath();
                
                const samples = Math.min(this.dataArrayLeft.length, this.dataArrayRight.length);
                let firstPoint = true;
                
                for (let i = 0; i < samples; i += this.samplingStep) { // Campionamento controllabile
                    const leftSample = (this.dataArrayLeft[i] - 128) / 128.0;
                    const rightSample = (this.dataArrayRight[i] - 128) / 128.0;
                    
                    const x = this.centerX + leftSample * this.scaleX;
                    const y = this.centerY - rightSample * this.scaleY;
                    
                    if (firstPoint) {
                        this.ctx.moveTo(x, y);
                        firstPoint = false;
                    } else {
                        this.ctx.lineTo(x, y);
                    }
                }
                
                this.ctx.stroke();

                // Aggiungi il tracciato corrente al trail con intensità controllata
                const trailBrightness = 0.3 + (this.phosphorLevel * 0.4); // Da 0.3 a 0.7
                this.trailCtx.strokeStyle = `rgba(0, ${Math.floor(170 * trailBrightness)}, 0, ${this.phosphorLevel})`;
                this.trailCtx.lineWidth = 0.5 + (this.phosphorLevel * 0.5);
                this.trailCtx.beginPath();
                
                firstPoint = true;
                for (let i = 0; i < samples; i += 2) {
                    const leftSample = (this.dataArrayLeft[i] - 128) / 128.0;
                    const rightSample = (this.dataArrayRight[i] - 128) / 128.0;
                    
                    const x = (this.centerX + leftSample * this.scaleX) * window.devicePixelRatio;
                    const y = (this.centerY - rightSample * this.scaleY) * window.devicePixelRatio;
                    
                    if (firstPoint) {
                        this.trailCtx.moveTo(x, y);
                        firstPoint = false;
                    } else {
                        this.trailCtx.lineTo(x, y);
                    }
                }
                this.trailCtx.stroke();
            }

            drawAxes() {
                this.ctx.strokeStyle = '#004400';
                this.ctx.lineWidth = 1;
                this.ctx.shadowBlur = 0;
                
                // Linea orizzontale
                this.ctx.beginPath();
                this.ctx.moveTo(0, this.centerY);
                this.ctx.lineTo(this.canvas.width / window.devicePixelRatio, this.centerY);
                this.ctx.stroke();
                
                // Linea verticale
                this.ctx.beginPath();
                this.ctx.moveTo(this.centerX, 0);
                this.ctx.lineTo(this.centerX, this.canvas.height / window.devicePixelRatio);
                this.ctx.stroke();

                // Griglia
                this.ctx.strokeStyle = '#002200';
                this.ctx.lineWidth = 0.5;
                
                for (let i = 1; i <= 4; i++) {
                    const offsetX = (this.scaleX / 2) * i;
                    const offsetY = (this.scaleY / 2) * i;
                    
                    // Linee verticali
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.centerX - offsetX, 0);
                    this.ctx.lineTo(this.centerX - offsetX, this.canvas.height / window.devicePixelRatio);
                    this.ctx.stroke();
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.centerX + offsetX, 0);
                    this.ctx.lineTo(this.centerX + offsetX, this.canvas.height / window.devicePixelRatio);
                    this.ctx.stroke();
                    
                    // Linee orizzontali
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, this.centerY - offsetY);
                    this.ctx.lineTo(this.canvas.width / window.devicePixelRatio, this.centerY - offsetY);
                    this.ctx.stroke();
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, this.centerY + offsetY);
                    this.ctx.lineTo(this.canvas.width / window.devicePixelRatio, this.centerY + offsetY);
                    this.ctx.stroke();
                }
            }

            stop() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }
                
                if (this.source) {
                    this.source.stop();
                    this.source = null;
                }
                
                if (this.audioContext && this.audioContext.state !== 'closed') {
                    this.audioContext.close();
                    this.audioContext = null;
                }
                
                // Clear canvas
                this.ctx.fillStyle = '#000';
                this.ctx.fillRect(0, 0, this.canvas.width / window.devicePixelRatio, this.canvas.height / window.devicePixelRatio);
                this.trailCtx.fillStyle = '#000';
                this.trailCtx.fillRect(0, 0, this.trailCanvas.width, this.trailCanvas.height);
                
                console.log('Riproduzione fermata');
            }
        }

        // Inizializza l'oscilloscopio quando la pagina è caricata
        window.addEventListener('load', () => {
            new AudioOscilloscope();
        });
    </script>
</body>
</html>