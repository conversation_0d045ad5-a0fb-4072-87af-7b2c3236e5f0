<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oscilloscopio 2D - <PERSON><PERSON> (Corretto)</title>
    <style>
        body { margin: 0; padding: 20px; background: #000; font-family: 'Courier New', monospace; color: #00ff00; overflow: hidden; display: flex; align-items: center; justify-content: center; min-height: 100vh; }
        .crt-container { position: relative; width: 80vw; height: 60vw; max-width: 900px; max-height: 675px; background: #000; border-radius: 10px; padding: 40px; box-shadow: 0 0 50px rgba(0, 255, 0, 0.3), inset 0 0 100px rgba(0, 50, 0, 0.5); }
        .screen { position: relative; width: 100%; height: 100%; background: radial-gradient(circle, #001100 0%, #000000 70%); border: 3px solid #333; overflow: hidden; }
        canvas { width: 100%; height: 100%; display: block; filter: brightness(1.2) contrast(1.1) drop-shadow(0 0 10px #00ff00); }
        .scanlines { position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; background: linear-gradient(transparent 50%, rgba(0, 255, 0, 0.03) 50%); background-size: 100% 4px; animation: scanlines 0.1s linear infinite; }
        .crt-flicker { position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; background: rgba(0, 255, 0, 0.02); animation: flicker 0.15s infinite alternate; }
        .controls { position: absolute; top: 10px; left: 10px; z-index: 10; display: flex; flex-direction: column; gap: 10px; }
        .control-group { display: flex; align-items: center; gap: 10px; }
        .slider-container { display: flex; align-items: center; gap: 8px; }
        input[type="range"] { -webkit-appearance: none; appearance: none; width: 120px; height: 4px; background: #003300; outline: none; border-radius: 2px; }
        input[type="range"]::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; width: 16px; height: 16px; background: #00ff00; cursor: pointer; border-radius: 50%; box-shadow: 0 0 8px rgba(0, 255, 0, 0.5); }
        .slider-label { color: #00aa00; font-size: 11px; min-width: 80px; }
        button { background: #001100; border: 1px solid #00ff00; color: #00ff00; padding: 8px 15px; margin: 5px; cursor: pointer; font-family: 'Courier New', monospace; transition: all 0.2s; }
        button:hover { background: #00ff00; color: #000; box-shadow: 0 0 10px #00ff00; }
        .info { position: absolute; bottom: 10px; left: 10px; font-size: 12px; color: #00aa00; }
        @keyframes scanlines { from { transform: translateY(0); } to { transform: translateY(4px); } }
        @keyframes flicker { from { opacity: 1; } to { opacity: 0.98; } }
    </style>
</head>
<body>
    <div class="crt-container">
        <div class="controls">
            <div class="control-group">
                <button id="stopBtn">Stop</button>
                <input type="file" id="audioFile" accept="audio/*" style="display: none;">
                <button id="loadFile">Carica File Audio</button>
            </div>
            <div class="slider-container">
                <span class="slider-label">Fluorescenza:</span>
                <input type="range" id="phosphorSlider" min="0" max="100" value="50">
                <span id="phosphorValue" class="slider-label">50%</span>
            </div>
        </div>
        
        <div class="screen">
            <canvas id="oscilloscope"></canvas>
            <div class="scanlines"></div>
            <div class="crt-flicker"></div>
        </div>
        
        <div class="info">
            X: Canale Sinistro | Y: Canale Destro | Oscilloscopio 2D - High Fidelity
        </div>
    </div>

    <script>
        const oscilloscopeProcessorCode = `
            class OscilloscopeProcessor extends AudioWorkletProcessor {
                process(inputs) {
                    const input = inputs[0];
                    if (!input || input.length === 0 || input[0].length === 0) {
                        return true;
                    }

                    const leftChannel = input[0];
                    const rightChannel = input.length > 1 ? input[1] : leftChannel;

                    this.port.postMessage({
                        left: leftChannel,
                        right: rightChannel
                    }, [leftChannel.buffer, rightChannel.buffer]);

                    return true;
                }
            }
            registerProcessor('oscilloscope-processor', OscilloscopeProcessor);
        `;

        class AudioOscilloscope {
            constructor() {
                this.canvas = document.getElementById('oscilloscope');
                this.ctx = this.canvas.getContext('2d');
                this.audioContext = null;
                this.animationId = null;
                this.phosphorLevel = 0.5;
                this.leftChannelData = [];
                this.rightChannelData = [];
                
                this.setupCanvas();
                this.setupEventListeners();
                this.drawInitialScreen();
            }

            setupCanvas() {
                const screen = document.querySelector('.screen');
                const rect = screen.getBoundingClientRect();
                const dpr = window.devicePixelRatio || 1;
                this.canvas.width = rect.width * dpr;
                this.canvas.height = rect.height * dpr;
                this.ctx.scale(dpr, dpr);
                
                this.width = rect.width;
                this.height = rect.height;
                this.centerX = this.width / 2;
                this.centerY = this.height / 2;
                this.scaleX = this.width / 2 * 0.9;
                this.scaleY = this.height / 2 * 0.9;
            }
            
            drawInitialScreen() {
                this.ctx.fillStyle = '#000';
                this.ctx.fillRect(0, 0, this.width, this.height);
                this.drawAxes();
            }

            setupEventListeners() {
                document.getElementById('stopBtn').addEventListener('click', () => this.stop());
                document.getElementById('loadFile').addEventListener('click', () => {
                    document.getElementById('audioFile').click();
                });
                document.getElementById('audioFile').addEventListener('change', (e) => this.handleFileSelection(e));
                
                const phosphorSlider = document.getElementById('phosphorSlider');
                phosphorSlider.addEventListener('input', (e) => {
                    this.phosphorLevel = e.target.value / 100;
                    document.getElementById('phosphorValue').textContent = e.target.value + '%';
                });
            }

            async handleFileSelection(event) {
                const file = event.target.files[0];
                if (!file) return;

                await this.stop();

                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

                    // <<< MODIFICA FONDAMENTALE: Attiva l'AudioContext se è sospeso
                    if (this.audioContext.state === 'suspended') {
                        console.log("AudioContext sospeso, tento di riprenderlo...");
                        await this.audioContext.resume();
                    }
                    console.log(`Stato AudioContext: ${this.audioContext.state}`);
                    if (this.audioContext.state !== 'running') {
                        throw new Error("L'AudioContext non è in esecuzione.");
                    }

                    const blob = new Blob([oscilloscopeProcessorCode], { type: 'application/javascript' });
                    const workletURL = URL.createObjectURL(blob);
                    await this.audioContext.audioWorklet.addModule(workletURL);
                    URL.revokeObjectURL(workletURL);

                    const arrayBuffer = await file.arrayBuffer();
                    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
                    
                    this.source = this.audioContext.createBufferSource();
                    this.source.buffer = audioBuffer;
                    this.source.loop = true;
                    
                    this.gainNode = this.audioContext.createGain();
                    this.gainNode.gain.value = 0.5;
                    
                    this.workletNode = new AudioWorkletNode(this.audioContext, 'oscilloscope-processor');
                    this.workletNode.port.onmessage = (e) => {
                        this.leftChannelData.push(e.data.left);
                        this.rightChannelData.push(e.data.right);
                    };

                    this.source.connect(this.gainNode).connect(this.audioContext.destination);
                    this.source.connect(this.workletNode);
                    
                    this.source.start();
                    this.startVisualization();
                    
                    console.log('▶️ Riproduzione e visualizzazione avviate.');

                } catch (error) {
                    console.error('❌ Errore durante l\'inizializzazione dell\'audio:', error);
                    alert('Impossibile avviare l\'audio: ' + error.message);
                    this.stop();
                }
            }

            startVisualization() {
                if (this.animationId) cancelAnimationFrame(this.animationId);
                const draw = () => {
                    this.drawOscilloscope();
                    this.animationId = requestAnimationFrame(draw);
                };
                draw();
            }

            drawOscilloscope() {
                const leftSamples = this.leftChannelData.flat();
                const rightSamples = this.rightChannelData.flat();

                this.leftChannelData = [];
                this.rightChannelData = [];
                
                // 1. Applica l'effetto dissolvenza (fosforo)
                this.ctx.globalCompositeOperation = 'source-over';
                const fadeAmount = (1 - this.phosphorLevel) * 0.7 + 0.05; // Mappato per essere più efficace
                this.ctx.fillStyle = `rgba(0, 0, 0, ${fadeAmount})`;
                this.ctx.fillRect(0, 0, this.width, this.height);

                // 2. Disegna la griglia
                this.drawAxes();

                // 3. Se non ci sono nuovi dati, esci
                if (leftSamples.length === 0) return;

                // 4. Disegna la nuova forma d'onda
                const brightness = 0.7 + (this.phosphorLevel * 0.3);
                this.ctx.strokeStyle = `rgba(150, 255, 150, ${brightness})`;
                this.ctx.lineWidth = 1.5 + this.phosphorLevel;
                this.ctx.shadowColor = '#00ff00';
                this.ctx.shadowBlur = this.phosphorLevel * 10;
                this.ctx.globalCompositeOperation = 'lighter'; // Effetto additivo per il look CRT

                this.ctx.beginPath();
                this.ctx.moveTo(
                    this.centerX + leftSamples[0] * this.scaleX,
                    this.centerY - rightSamples[0] * this.scaleY
                );
                
                for (let i = 1; i < leftSamples.length; i++) {
                    this.ctx.lineTo(
                        this.centerX + leftSamples[i] * this.scaleX,
                        this.centerY - rightSamples[i] * this.scaleY
                    );
                }
                this.ctx.stroke();

                // Reset per il prossimo frame
                this.ctx.shadowBlur = 0;
            }

            drawAxes() {
                this.ctx.strokeStyle = '#003300';
                this.ctx.lineWidth = 1;
                this.ctx.shadowBlur = 0;
                this.ctx.globalCompositeOperation = 'source-over';
                const gridDivisions = 8;
                const stepX = this.width / gridDivisions;
                const stepY = this.height / gridDivisions;
                for (let i = 1; i < gridDivisions; i++) {
                    this.ctx.beginPath();
                    this.ctx.moveTo(i * stepX, 0);
                    this.ctx.lineTo(i * stepX, this.height);
                    this.ctx.stroke();
                    this.ctx.beginPath();
                    this.ctx.moveTo(0, i * stepY);
                    this.ctx.lineTo(this.width, i * stepY);
                    this.ctx.stroke();
                }
            }

            async stop() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                    this.animationId = null;
                }
                
                if (this.audioContext) {
                    await this.audioContext.close();
                    this.audioContext = null;
                }
                
                this.drawInitialScreen();
                console.log('⏹️ Riproduzione e visualizzazione fermate.');
            }
        }

        window.addEventListener('load', () => {
            new AudioOscilloscope();
        });
    </script>
</body>
</html>