#!/usr/bin/env python3
"""
Oscilloscopio XY 2D – GUI Tkinter
- Asse X = canale sinistro
- Asse Y = canale destro
- Riproduzione a frequenza massima supportata dal dispositivo
- Nessun microfono: sorgente = file audio
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import sounddevice as sd
import librosa
import numpy as np
import matplotlib
matplotlib.use("TkAgg")
import matplotlib.pyplot as plt
import queue
import sys
import os

# -------------------------------------------------
# 1) GUI principale
# -------------------------------------------------
class OscGUI(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Oscilloscopio XY – File picker")
        self.geometry("260x100")
        self.resizable(False, False)

        tk.Button(self, text="Scegli file audio…",
                  command=self.load_and_run,
                  width=20, height=2, font=("Segoe UI", 12)).pack(pady=20)

        self.protocol("WM_DELETE_WINDOW", self.on_exit)

    # ---------------------------------------------
    def load_and_run(self):
        filepath = filedialog.askopenfilename(
            title="Seleziona file audio",
            filetypes=[("Audio files", "*.wav *.flac *.mp3 *.ogg *.m4a"),
                       ("All files", "*.*")])
        if not filepath:
            return
        if not os.path.isfile(filepath):
            messagebox.showerror("Errore", "File non trovato.")
            return

        self.withdraw()          # nascondi la GUI
        try:
            Oscilloscope(filepath, self)
        except Exception as e:
            messagebox.showerror("Errore", str(e))
            self.deiconify()

    # ---------------------------------------------
    def on_exit(self):
        plt.close('all')
        self.quit()

# -------------------------------------------------
# 2) Motore oscilloscopio (callback-only)
# -------------------------------------------------
class Oscilloscope:
    def __init__(self, audio_path, root):
        self.root = root
        self.audio_path = audio_path
        self.idx = 0
        self.q = queue.Queue()

        # ------------------ caricamento file
        # --- dentro __init__ di Oscilloscope, sostituire il blocco “caricamento + frequenza” ---
        print("Loading audio…")
        y, sr_native = librosa.load(audio_path, sr=None, mono=False)
        if y.ndim == 1:
            y = np.vstack((y, y))
        y = y.astype(np.float32)

        DESIRED_SR = 96000      # frequenza di lavoro dell’oscilloscopio

        # 1) Resample interno a 96 kHz (o lascia nativo se già 96 k)
        if sr_native != DESIRED_SR:
            print("Resampling data to", DESIRED_SR)
            y = librosa.resample(y, orig_sr=sr_native, target_sr=DESIRED_SR)
        data_sr = DESIRED_SR
        self.y = y.T

        # 2) Trova la frequenza effettiva di playback
        try:
            # prova ad aprire a 96 kHz
            sd.check_output_settings(samplerate=data_sr)
            playback_sr = data_sr
        except ValueError:
            # il dispositivo non la supporta → usa la massima offerta
            playback_sr = int(sd.query_devices(sd.default.device[1], 'output')['default_samplerate'])
        print(f"Data sr: {data_sr} Hz  –  Playback sr: {playback_sr} Hz")

        # 3) Avvia lo stream alla frequenza reale del dispositivo
        self.stream = sd.OutputStream(
            samplerate=playback_sr,
            channels=2,
            callback=self.audio_callback,
            blocksize=2048,
            latency='low',
            finished_callback=self.playback_finished
        )
        self.stream.start()

        # ------------------ prepara grafico
        plt.style.use("dark_background")
        self.fig, self.ax = plt.subplots(figsize=(6, 6))
        self.ax.set_xlim(-1, 1)
        self.ax.set_ylim(-1, 1)
        self.ax.set_aspect('equal')
        self.ax.set_xlabel("Left  (X)")
        self.ax.set_ylabel("Right (Y)")
        self.ax.set_title(f"{os.path.basename(audio_path)} – "
                          f"{self.target_sr/1000:.0f} kHz XY")
        self.sc = self.ax.scatter([], [], s=0.3, color="cyan")
        self.fig.canvas.manager.set_window_title("Oscilloscopio XY")
        self.update_plot()

    # ---------------------------------------------
    def audio_callback(self, outdata, frames, time, status):
        if status:
            print(status, file=sys.stderr)

        chunk = self.y[self.idx:self.idx+frames]
        if len(chunk) < frames:                # fine file
            outdata[:len(chunk)] = chunk
            outdata[len(chunk):] = 0
            raise sd.CallbackStop
        else:
            outdata[:] = chunk
        self.idx += frames
        self.q.put(outdata.copy())             # invia al plot

    # ---------------------------------------------
    def update_plot(self):
        while True:
            try:
                data = self.q.get(timeout=0.1)
            except queue.Empty:
                continue
            if data is None:                  # segnale di chiusura
                break
            self.sc.set_offsets(np.c_[data[:, 0], data[:, 1]])
            plt.pause(0.001)
        plt.close(self.fig)

    # ---------------------------------------------
    def playback_finished(self):
        # chiamato quando lo stream si ferma
        self.q.put(None)
        self.root.after(0, self.root.deiconify)

# -------------------------------------------------
# 3) Avvio
# -------------------------------------------------
if __name__ == "__main__":
    OscGUI().mainloop()