"""
World of Tanks Replay Parser
O<PERSON>zzato per l'estrazione dati per AI Advisor

Basato sui migliori progetti della community:
- wotdecoder (raszpl)
- wotreplay (gabriel-oana) 
- wot_replay_parser (Rust)
"""

import struct
import json
import zlib
import pickle
import io
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ReplayMetadata:
    """Metadati essenziali del replay"""
    replay_version: str
    map_name: str
    battle_mode: str
    battle_type: str
    duration: float
    result: str  # Win/Loss/Draw
    player_name: str
    player_tank: str
    battle_start_time: datetime
    arena_unique_id: int

@dataclass
class PlayerData:
    """Dati di un giocatore"""
    name: str
    clan: str
    tank: str
    tier: int
    team: int
    kills: int = 0
    damage: int = 0
    spotted: int = 0
    survived: bool = False
    
@dataclass
class BattleEvent:
    """Evento durante la battaglia"""
    timestamp: float
    event_type: str
    data: Dict[str, Any]

class WoTReplayParser:
    """Parser principale per file .wotreplay"""
    
    MAGIC_NUMBER = 0x12323411
    
    def __init__(self, replay_path: str):
        self.replay_path = Path(replay_path)
        self.raw_data: bytes = b""
        self.json_blocks: List[Dict] = []
        self.binary_data: bytes = b""
        self.metadata: Optional[ReplayMetadata] = None
        self.players: Dict[int, PlayerData] = {}
        self.events: List[BattleEvent] = []
        
    def parse(self) -> bool:
        """Parsing completo del replay"""
        try:
            logger.info(f"Parsing replay: {self.replay_path}")
            
            # Leggi file
            with open(self.replay_path, 'rb') as f:
                self.raw_data = f.read()
            
            # Parse header e sezioni
            self._parse_header()
            self._parse_json_sections()
            self._extract_metadata()
            self._extract_players()
            
            # Parse sezione binaria se disponibile
            if self.binary_data:
                self._parse_binary_section()
                
            logger.info("Parsing completato con successo")
            return True
            
        except Exception as e:
            logger.error(f"Errore durante il parsing: {e}")
            return False
    
    def _parse_header(self):
        """Parse header del file"""
        with io.BytesIO(self.raw_data) as f:
            # DEBUG: Mostra i primi bytes
            first_bytes = self.raw_data[:16]
            logger.info(f"Prime 16 bytes (hex): {first_bytes.hex()}")
            logger.info(f"Prime 16 bytes (int): {[x for x in first_bytes]}")
            
            # Prova diversi magic numbers conosciuti
            magic_bytes = f.read(4)
            magic_le = struct.unpack('<I', magic_bytes)[0]  # Little endian
            magic_be = struct.unpack('>I', magic_bytes)[0]  # Big endian
            
            logger.info(f"Magic number (little endian): {magic_le} (0x{magic_le:08X})")
            logger.info(f"Magic number (big endian): {magic_be} (0x{magic_be:08X})")
            
            # Controlla magic numbers conosciuti
            known_magics = {
                0x12323411: "Standard replay format",
                0x11343212: "Reverse endian or different format",
                0x504B0304: "ZIP file (possible compressed replay)",
                0x78DA: "ZLIB compressed start",
            }
            
            magic_found = False
            for known_magic, description in known_magics.items():
                if magic_le == known_magic or magic_be == known_magic:
                    logger.info(f"Formato riconosciuto: {description}")
                    magic_found = True
                    break
            
            if not magic_found:
                # Proviamo a vedere se è JSON diretto
                try:
                    f.seek(0)
                    test_data = f.read(100).decode('utf-8', errors='ignore')
                    if test_data.strip().startswith('{'):
                        logger.info("Il file sembra essere JSON puro")
                        self._parse_json_only()
                        return
                except:
                    pass
                
                # Se non riconosciamo il formato, continua comunque per debug
                logger.warning(f"Magic number sconosciuto, tentativo parsing con {magic_le:08X}")
            
            # Reset al magic number standard o usa quello trovato
            magic = magic_le if magic_le == self.MAGIC_NUMBER else magic_le
            
            # Leggi numero di blocchi JSON
            try:
                block_count = struct.unpack('<I', f.read(4))[0]
                logger.info(f"Numero blocchi JSON: {block_count}")
                
                if block_count > 100 or block_count <= 0:
                    logger.warning(f"Numero blocchi sospetto: {block_count}")
                    # Prova big endian
                    f.seek(4)
                    block_count = struct.unpack('>I', f.read(4))[0]
                    logger.info(f"Numero blocchi (big endian): {block_count}")
                    
            except struct.error as e:
                logger.error(f"Errore lettura block count: {e}")
                return
            
            
            # Leggi ogni blocco JSON
            for i in range(min(block_count, 10)):  # Limita a 10 blocchi per sicurezza
                try:
                    block_size = struct.unpack('<I', f.read(4))[0]
                    logger.info(f"Blocco {i}: dimensione {block_size} bytes")
                    
                    if block_size > len(self.raw_data) or block_size <= 0:
                        logger.warning(f"Dimensione blocco {i} invalida: {block_size}")
                        break
                        
                    block_data = f.read(block_size)
                    
                    try:
                        # Prova a decodificare come JSON
                        json_text = block_data.decode('utf-8')
                        json_data = json.loads(json_text)
                        self.json_blocks.append(json_data)
                        logger.info(f"Blocco {i} parsato come JSON ({len(json_data)} chiavi)")
                        
                    except (json.JSONDecodeError, UnicodeDecodeError) as e:
                        logger.warning(f"Blocco {i} non è JSON valido: {e}")
                        # Prova decompressione
                        try:
                            decompressed = zlib.decompress(block_data)
                            json_data = json.loads(decompressed.decode('utf-8'))
                            self.json_blocks.append(json_data)
                            logger.info(f"Blocco {i} parsato come JSON compresso")
                        except:
                            # Salva come raw data
                            self.json_blocks.append({"raw_data": block_data, "block_id": i})
                            logger.warning(f"Blocco {i} salvato come raw data")
                            
                except struct.error as e:
                    logger.error(f"Errore strutturale blocco {i}: {e}")
                    break
                except Exception as e:
                    logger.error(f"Errore generico blocco {i}: {e}")
                    break
            
            # Il resto è la sezione binaria
            current_pos = f.tell()
            remaining_data = f.read()
            self.binary_data = remaining_data
            logger.info(f"Sezione binaria: {len(self.binary_data)} bytes")
    
    def _parse_json_only(self):
        """Parse se il file è solo JSON"""
        try:
            json_data = json.loads(self.raw_data.decode('utf-8'))
            self.json_blocks = [json_data]
            logger.info("File parsato come JSON puro")
        except Exception as e:
            logger.error(f"Errore parsing JSON puro: {e}")
            
    def _parse_json_sections(self):
        """Analizza le sezioni JSON"""
        logger.info(f"Analizzando {len(self.json_blocks)} sezioni JSON")
        
        for i, block in enumerate(self.json_blocks):
            if isinstance(block, dict) and 'raw_data' not in block:
                logger.info(f"Blocco {i}: {len(block)} chiavi")
                # Log delle chiavi principali
                if len(block) < 20:  # Solo per blocchi piccoli
                    logger.debug(f"Chiavi: {list(block.keys())}")
    
    def _extract_metadata(self):
        """Estrae metadati essenziali"""
        try:
            # Prendi il primo blocco JSON (solitamente i metadati)
            main_block = self.json_blocks[0] if self.json_blocks else {}
            
            # Estrai informazioni base
            replay_version = main_block.get('clientVersionFromExe', 'unknown')
            map_name = main_block.get('mapDisplayName', 'unknown')
            battle_mode = main_block.get('battleType', 'unknown') 
            battle_type = main_block.get('gameplayID', 'unknown')
            
            # Trova il giocatore principale (owner del replay)
            player_name = main_block.get('playerName', 'unknown')
            player_vehicle = main_block.get('playerVehicle', 'unknown')
            
            # Data battaglia
            date_time = main_block.get('dateTime', '')
            try:
                battle_start = datetime.strptime(date_time, '%d.%m.%Y %H:%M:%S')
            except:
                battle_start = datetime.now()
            
            self.metadata = ReplayMetadata(
                replay_version=replay_version,
                map_name=map_name,
                battle_mode=battle_mode,
                battle_type=battle_type,
                duration=0.0,  # Verrà calcolato dalla sezione binaria
                result='unknown',  # Verrà estratto dai risultati
                player_name=player_name,
                player_tank=player_vehicle,
                battle_start_time=battle_start,
                arena_unique_id=main_block.get('arenaUniqueID', 0)
            )
            
            logger.info(f"Metadata estratti: {player_name} su {player_vehicle}")
            
        except Exception as e:
            logger.error(f"Errore nell'estrazione metadata: {e}")
    
    def _extract_players(self):
        """Estrae dati dei giocatori"""
        try:
            # Cerca il blocco con i veicoli
            for block in self.json_blocks:
                if 'vehicles' in block:
                    vehicles = block['vehicles']
                    logger.info(f"Trovati {len(vehicles)} veicoli")
                    
                    for vehicle_id, vehicle_data in vehicles.items():
                        try:
                            player = PlayerData(
                                name=vehicle_data.get('name', 'unknown'),
                                clan=vehicle_data.get('clanAbbrev', ''),
                                tank=vehicle_data.get('vehicleType', 'unknown'),
                                tier=vehicle_data.get('level', 0),
                                team=vehicle_data.get('team', 0)
                            )
                            self.players[int(vehicle_id)] = player
                            
                        except Exception as e:
                            logger.warning(f"Errore parsing veicolo {vehicle_id}: {e}")
                    
                    break
                    
        except Exception as e:
            logger.error(f"Errore nell'estrazione giocatori: {e}")
    
    def _parse_binary_section(self):
        """Parse della sezione binaria (packets di gioco)"""
        if not self.binary_data:
            logger.warning("Nessuna sezione binaria trovata")
            return
            
        logger.info(f"Parsing sezione binaria ({len(self.binary_data)} bytes)")
        
        try:
            # La sezione binaria può essere compressa
            try:
                decompressed = zlib.decompress(self.binary_data)
                logger.info(f"Sezione decompressa: {len(decompressed)} bytes")
                self._parse_packets(decompressed)
            except zlib.error:
                logger.info("Sezione non compressa, parsing diretto")
                self._parse_packets(self.binary_data)
                
        except Exception as e:
            logger.error(f"Errore parsing sezione binaria: {e}")
    
    def _parse_packets(self, data: bytes):
        """Parse dei packets dalla sezione binaria"""
        # Questo è molto complesso e specifico per versione
        # Per ora implementiamo solo il riconoscimento di base
        
        with io.BytesIO(data) as f:
            packet_count = 0
            
            while f.tell() < len(data) - 8:  # Almeno 8 bytes rimasti
                try:
                    # Leggi header packet generico
                    packet_type = struct.unpack('<I', f.read(4))[0]
                    packet_size = struct.unpack('<I', f.read(4))[0]
                    
                    # Controllo sanity
                    if packet_size > len(data) or packet_size == 0:
                        break
                        
                    packet_data = f.read(packet_size)
                    
                    # Crea evento generico
                    event = BattleEvent(
                        timestamp=packet_count * 0.1,  # Timestamp approssimato
                        event_type=f"packet_{packet_type}",
                        data={"size": packet_size, "raw": packet_data[:100]}  # Solo primi 100 bytes
                    )
                    
                    self.events.append(event)
                    packet_count += 1
                    
                    if packet_count % 1000 == 0:
                        logger.info(f"Processati {packet_count} packets...")
                        
                except struct.error:
                    break
                except Exception as e:
                    logger.warning(f"Errore packet {packet_count}: {e}")
                    break
            
            logger.info(f"Totale packets processati: {packet_count}")
    
    def get_summary(self) -> Dict[str, Any]:
        """Ritorna riassunto del replay per AI"""
        summary = {
            "metadata": {
                "version": self.metadata.replay_version if self.metadata else "unknown",
                "map": self.metadata.map_name if self.metadata else "unknown", 
                "mode": self.metadata.battle_mode if self.metadata else "unknown",
                "player": self.metadata.player_name if self.metadata else "unknown",
                "tank": self.metadata.player_tank if self.metadata else "unknown",
                "arena_id": self.metadata.arena_unique_id if self.metadata else 0,
            },
            "players": {
                "count": len(self.players),
                "teams": len(set(p.team for p in self.players.values())),
                "players": [
                    {
                        "name": p.name,
                        "clan": p.clan,
                        "tank": p.tank,
                        "tier": p.tier,
                        "team": p.team
                    }
                    for p in self.players.values()
                ]
            },
            "events": {
                "count": len(self.events),
                "types": list(set(e.event_type for e in self.events))
            }
        }
        
        return summary
    
    def export_for_ai_training(self) -> Dict[str, Any]:
        """Esporta dati in formato ottimale per training AI"""
        
        def make_json_serializable(obj):
            """Converte oggetti non serializzabili in JSON"""
            if isinstance(obj, bytes):
                return obj.hex()  # Converte bytes in hex string
            elif isinstance(obj, dict):
                return {k: make_json_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [make_json_serializable(item) for item in obj]
            else:
                return obj
        
        # Estrai statistiche dettagliate dai veicoli
        team_stats = {}
        for team in set(p.team for p in self.players.values()):
            team_vehicles = [p for p in self.players.values() if p.team == team]
            team_stats[team] = {
                "vehicles": [{"tank": p.tank, "tier": p.tier, "player": p.name, "clan": p.clan} 
                           for p in team_vehicles],
                "total_vehicles": len(team_vehicles),
                "tiers": [p.tier for p in team_vehicles if p.tier > 0],
                "tank_classes": self._categorize_tanks([p.tank for p in team_vehicles])
            }
        
        return {
            "battle_info": {
                "map": self.metadata.map_name if self.metadata else "unknown",
                "mode": str(self.metadata.battle_mode) if self.metadata else "unknown",
                "player": self.metadata.player_name if self.metadata else "unknown",
                "player_tank": self.metadata.player_tank if self.metadata else "unknown",
                "replay_version": self.metadata.replay_version if self.metadata else "unknown",
                "duration_estimate": len(self.events) * 0.1,  # Durata approssimata
                "arena_id": self.metadata.arena_unique_id if self.metadata else 0,
            },
            "team_composition": team_stats,
            "battle_statistics": {
                "total_players": len(self.players),
                "teams": len(set(p.team for p in self.players.values())),
                "clans_present": len(set(p.clan for p in self.players.values() if p.clan)),
                "nations": list(set(p.tank.split(':')[0] if ':' in p.tank else 'unknown' 
                                  for p in self.players.values())),
            },
            "events_summary": {
                "total_events": len(self.events),
                "event_types": list(set(e.event_type for e in self.events)),
                "binary_data_size": len(self.binary_data),
                "first_events": [
                    {
                        "time": e.timestamp,
                        "type": e.event_type,
                        "data_size": len(str(e.data)) if e.data else 0,
                        # Serializza solo dati sicuri
                        "data": make_json_serializable(
                            {k: v for k, v in e.data.items() if k != 'raw'}
                        ) if isinstance(e.data, dict) else str(e.data)[:100]
                    }
                    for e in self.events[:10]  # Solo primi 10 per sicurezza
                ]
            }
        }
    
    def _categorize_tanks(self, tank_list: List[str]) -> Dict[str, int]:
        """Categorizza i carri per classe (approssimativa)"""
        categories = {
            "heavy": 0,
            "medium": 0, 
            "light": 0,
            "tank_destroyer": 0,
            "artillery": 0,
            "unknown": 0
        }
        
        for tank in tank_list:
            tank_lower = tank.lower()
            if any(x in tank_lower for x in ['kv', 'is-', 'tiger', 'churchill', 't29', 't32']):
                categories["heavy"] += 1
            elif any(x in tank_lower for x in ['t-34', 'm4', 'panzer', 'sherman', 'cromwell']):
                categories["medium"] += 1
            elif any(x in tank_lower for x in ['chaffee', 'luchs', 'bt-', 'elc', 't37']):
                categories["light"] += 1
            elif any(x in tank_lower for x in ['stug', 'jagd', 'su-', 'hellcat', 'at-']):
                categories["tank_destroyer"] += 1
            elif any(x in tank_lower for x in ['lefh', 'su-26', 'wespe', 'bishop', 'fv304']):
                categories["artillery"] += 1
            else:
                categories["unknown"] += 1
                
        return categories

# Utility function per batch processing
def parse_multiple_replays(replay_dir: str, output_dir: str = None):
    """Parse multipli replay e salva risultati"""
    replay_path = Path(replay_dir)
    results = []
    
    replay_files = list(replay_path.glob("*.wotreplay"))
    logger.info(f"Trovati {len(replay_files)} replay files")
    
    for replay_file in replay_files:
        parser = WoTReplayParser(str(replay_file))
        
        if parser.parse():
            summary = parser.get_summary()
            summary["filename"] = replay_file.name
            results.append(summary)
            
            # Salva individualmente se richiesto
            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(exist_ok=True)
                
                json_file = output_path / f"{replay_file.stem}.json"
                with open(json_file, 'w') as f:
                    json.dump(parser.export_for_ai_training(), f, indent=2)
        
        else:
            logger.error(f"Fallito parsing di {replay_file}")
    
    logger.info(f"Parsing completato: {len(results)}/{len(replay_files)} successi")
    return results

# Utility function per creare file di test
def create_test_replay(filename: str = "test_replay.wotreplay"):
    """Crea un replay di test per verificare il parser"""
    
    # Dati di test simulati
    test_data = {
        "clientVersionFromExe": "1.23.1.0",
        "mapDisplayName": "Himmelsdorf",
        "battleType": "random",
        "gameplayID": "ctf",
        "playerName": "TestPlayer",
        "playerVehicle": "germany-Pz_VI_Tiger_I",
        "dateTime": "24.08.2025 14:30:45",
        "arenaUniqueID": 12345678,
        "vehicles": {
            "1": {
                "name": "TestPlayer",
                "clanAbbrev": "TEST",
                "vehicleType": "germany-Pz_VI_Tiger_I",
                "level": 7,
                "team": 1
            },
            "2": {
                "name": "Enemy1", 
                "clanAbbrev": "FOE",
                "vehicleType": "ussr-T-34-85",
                "level": 6,
                "team": 2
            }
        }
    }
    
    # Crea file con formato corretto
    with open(filename, 'wb') as f:
        # Magic number
        f.write(struct.pack('<I', WoTReplayParser.MAGIC_NUMBER))
        
        # Un blocco JSON
        json_data = json.dumps(test_data).encode('utf-8')
        f.write(struct.pack('<I', 1))  # 1 blocco
        f.write(struct.pack('<I', len(json_data)))  # Dimensione blocco
        f.write(json_data)
        
        # Sezione binaria fake (alcuni packet di test)
        binary_data = b""
        for i in range(10):
            packet = struct.pack('<II', i, 16) + b"test_packet_data"
            binary_data += packet
            
        f.write(binary_data)
    
    logger.info(f"File di test creato: {filename}")
    return filename

# Esempio di uso
if __name__ == "__main__":
    import sys
    
    replay_file = "example.wotreplay"
    
    # Se il file non esiste, crea uno di test
    if not Path(replay_file).exists():
        logger.info("File example.wotreplay non trovato, creo file di test")
        replay_file = create_test_replay(replay_file)
    
    # Test singolo replay
    parser = WoTReplayParser(replay_file)
    
    if parser.parse():
        summary = parser.get_summary()
        print("=== RIASSUNTO REPLAY ===")
        print(json.dumps(summary, indent=2))
        
        # Esporta per AI training
        ai_data = parser.export_for_ai_training()
        print("\n=== DATI PER AI ===")
        print(json.dumps(ai_data, indent=2))
    else:
        print("Parsing fallito - controlla i log per dettagli")
    
    # Test batch processing
    # results = parse_multiple_replays("./replays", "./parsed_data")