"""
World of Tanks Replay Parser
O<PERSON>zzato per l'estrazione dati per AI Advisor

Basato sui migliori progetti della community:
- wotdecoder (raszpl)
- wotreplay (gabriel-oana) 
- wot_replay_parser (Rust)
"""

import struct
import json
import zlib
import pickle
import io
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ReplayMetadata:
    """Metadati essenziali del replay"""
    replay_version: str
    map_name: str
    battle_mode: str
    battle_type: str
    duration: float
    result: str  # Win/Loss/Draw
    player_name: str
    player_tank: str
    battle_start_time: datetime
    arena_unique_id: int

@dataclass
class PlayerData:
    """Dati di un giocatore"""
    name: str
    clan: str
    tank: str
    tier: int
    team: int
    kills: int = 0
    damage: int = 0
    spotted: int = 0
    survived: bool = False
    
@dataclass
class BattleEvent:
    """Evento durante la battaglia"""
    timestamp: float
    event_type: str
    data: Dict[str, Any]

class WoTReplayParser:
    """Parser principale per file .wotreplay"""
    
    MAGIC_NUMBER = 0x12323411
    
    def __init__(self, replay_path: str):
        self.replay_path = Path(replay_path)
        self.raw_data: bytes = b""
        self.json_blocks: List[Dict] = []
        self.binary_data: bytes = b""
        self.metadata: Optional[ReplayMetadata] = None
        self.players: Dict[int, PlayerData] = {}
        self.events: List[BattleEvent] = []
        
    def parse(self) -> bool:
        """Parsing completo del replay"""
        try:
            logger.info(f"Parsing replay: {self.replay_path}")
            
            # Leggi file
            with open(self.replay_path, 'rb') as f:
                self.raw_data = f.read()
            
            # Parse header e sezioni
            self._parse_header()
            self._parse_json_sections()
            self._extract_metadata()
            self._extract_players()
            
            # Parse sezione binaria se disponibile
            if self.binary_data:
                self._parse_binary_section()
                
            logger.info("Parsing completato con successo")
            return True
            
        except Exception as e:
            logger.error(f"Errore durante il parsing: {e}")
            return False
    
    def _parse_header(self):
        """Parse header del file"""
        with io.BytesIO(self.raw_data) as f:
            # DEBUG: Mostra i primi bytes
            first_bytes = self.raw_data[:16]
            logger.info(f"Prime 16 bytes (hex): {first_bytes.hex()}")
            logger.info(f"Prime 16 bytes (int): {[x for x in first_bytes]}")
            
            # Prova diversi magic numbers conosciuti
            magic_bytes = f.read(4)
            magic_le = struct.unpack('<I', magic_bytes)[0]  # Little endian
            magic_be = struct.unpack('>I', magic_bytes)[0]  # Big endian
            
            logger.info(f"Magic number (little endian): {magic_le} (0x{magic_le:08X})")
            logger.info(f"Magic number (big endian): {magic_be} (0x{magic_be:08X})")
            
            # Controlla magic numbers conosciuti
            known_magics = {
                0x12323411: "Standard replay format",
                0x11343212: "Reverse endian or different format",
                0x504B0304: "ZIP file (possible compressed replay)",
                0x78DA: "ZLIB compressed start",
            }
            
            magic_found = False
            for known_magic, description in known_magics.items():
                if magic_le == known_magic or magic_be == known_magic:
                    logger.info(f"Formato riconosciuto: {description}")
                    magic_found = True
                    break
            
            if not magic_found:
                # Proviamo a vedere se è JSON diretto
                try:
                    f.seek(0)
                    test_data = f.read(100).decode('utf-8', errors='ignore')
                    if test_data.strip().startswith('{'):
                        logger.info("Il file sembra essere JSON puro")
                        self._parse_json_only()
                        return
                except:
                    pass
                
                # Se non riconosciamo il formato, continua comunque per debug
                logger.warning(f"Magic number sconosciuto, tentativo parsing con {magic_le:08X}")
            
            # Reset al magic number standard o usa quello trovato
            magic = magic_le if magic_le == self.MAGIC_NUMBER else magic_le
            
            # Leggi numero di blocchi JSON
            try:
                block_count = struct.unpack('<I', f.read(4))[0]
                logger.info(f"Numero blocchi JSON: {block_count}")
                
                if block_count > 100 or block_count <= 0:
                    logger.warning(f"Numero blocchi sospetto: {block_count}")
                    # Prova big endian
                    f.seek(4)
                    block_count = struct.unpack('>I', f.read(4))[0]
                    logger.info(f"Numero blocchi (big endian): {block_count}")
                    
            except struct.error as e:
                logger.error(f"Errore lettura block count: {e}")
                return
            
            
            # Leggi ogni blocco JSON
            for i in range(min(block_count, 10)):  # Limita a 10 blocchi per sicurezza
                try:
                    block_size = struct.unpack('<I', f.read(4))[0]
                    logger.info(f"Blocco {i}: dimensione {block_size} bytes")
                    
                    if block_size > len(self.raw_data) or block_size <= 0:
                        logger.warning(f"Dimensione blocco {i} invalida: {block_size}")
                        break
                        
                    block_data = f.read(block_size)
                    
                    try:
                        # Prova a decodificare come JSON
                        json_text = block_data.decode('utf-8')
                        json_data = json.loads(json_text)
                        self.json_blocks.append(json_data)
                        logger.info(f"Blocco {i} parsato come JSON ({len(json_data)} chiavi)")
                        
                    except (json.JSONDecodeError, UnicodeDecodeError) as e:
                        logger.warning(f"Blocco {i} non è JSON valido: {e}")
                        # Prova decompressione
                        try:
                            decompressed = zlib.decompress(block_data)
                            json_data = json.loads(decompressed.decode('utf-8'))
                            self.json_blocks.append(json_data)
                            logger.info(f"Blocco {i} parsato come JSON compresso")
                        except:
                            # Salva come raw data
                            self.json_blocks.append({"raw_data": block_data, "block_id": i})
                            logger.warning(f"Blocco {i} salvato come raw data")
                            
                except struct.error as e:
                    logger.error(f"Errore strutturale blocco {i}: {e}")
                    break
                except Exception as e:
                    logger.error(f"Errore generico blocco {i}: {e}")
                    break
            
            # Il resto è la sezione binaria
            current_pos = f.tell()
            remaining_data = f.read()
            self.binary_data = remaining_data
            logger.info(f"Sezione binaria: {len(self.binary_data)} bytes")
    
    def _parse_json_only(self):
        """Parse se il file è solo JSON"""
        try:
            json_data = json.loads(self.raw_data.decode('utf-8'))
            self.json_blocks = [json_data]
            logger.info("File parsato come JSON puro")
        except Exception as e:
            logger.error(f"Errore parsing JSON puro: {e}")
            
    def _parse_json_sections(self):
        """Analizza le sezioni JSON"""
        logger.info(f"Analizzando {len(self.json_blocks)} sezioni JSON")
        
        for i, block in enumerate(self.json_blocks):
            if isinstance(block, dict) and 'raw_data' not in block:
                logger.info(f"Blocco {i}: {len(block)} chiavi")
                # Log delle chiavi principali
                if len(block) < 20:  # Solo per blocchi piccoli
                    logger.debug(f"Chiavi: {list(block.keys())}")
    
    def _extract_metadata(self):
        """Estrae metadati essenziali"""
        try:
            # Prendi il primo blocco JSON (solitamente i metadati)
            main_block = self.json_blocks[0] if self.json_blocks else {}
            
            # Estrai informazioni base
            replay_version = main_block.get('clientVersionFromExe', 'unknown')
            map_name = main_block.get('mapDisplayName', 'unknown')
            battle_mode = main_block.get('battleType', 'unknown') 
            battle_type = main_block.get('gameplayID', 'unknown')
            
            # Trova il giocatore principale (owner del replay)
            player_name = main_block.get('playerName', 'unknown')
            player_vehicle = main_block.get('playerVehicle', 'unknown')
            
            # Data battaglia
            date_time = main_block.get('dateTime', '')
            try:
                battle_start = datetime.strptime(date_time, '%d.%m.%Y %H:%M:%S')
            except:
                battle_start = datetime.now()
            
            self.metadata = ReplayMetadata(
                replay_version=replay_version,
                map_name=map_name,
                battle_mode=battle_mode,
                battle_type=battle_type,
                duration=0.0,  # Verrà calcolato dalla sezione binaria
                result='unknown',  # Verrà estratto dai risultati
                player_name=player_name,
                player_tank=player_vehicle,
                battle_start_time=battle_start,
                arena_unique_id=main_block.get('arenaUniqueID', 0)
            )
            
            logger.info(f"Metadata estratti: {player_name} su {player_vehicle}")
            
        except Exception as e:
            logger.error(f"Errore nell'estrazione metadata: {e}")
    
    def _extract_players(self):
        """Estrae dati dei giocatori"""
        try:
            # Cerca il blocco con i veicoli
            for block in self.json_blocks:
                if 'vehicles' in block:
                    vehicles = block['vehicles']
                    logger.info(f"Trovati {len(vehicles)} veicoli")
                    
                    for vehicle_id, vehicle_data in vehicles.items():
                        try:
                            player = PlayerData(
                                name=vehicle_data.get('name', 'unknown'),
                                clan=vehicle_data.get('clanAbbrev', ''),
                                tank=vehicle_data.get('vehicleType', 'unknown'),
                                tier=vehicle_data.get('level', 0),
                                team=vehicle_data.get('team', 0)
                            )
                            self.players[int(vehicle_id)] = player
                            
                        except Exception as e:
                            logger.warning(f"Errore parsing veicolo {vehicle_id}: {e}")
                    
                    break
                    
        except Exception as e:
            logger.error(f"Errore nell'estrazione giocatori: {e}")
    
    def _parse_binary_section(self):
        """Parse della sezione binaria (packets di gioco)"""
        if not self.binary_data:
            logger.warning("Nessuna sezione binaria trovata")
            return
            
        logger.info(f"Parsing sezione binaria ({len(self.binary_data)} bytes)")
        
        try:
            # La sezione binaria può essere compressa
            try:
                decompressed = zlib.decompress(self.binary_data)
                logger.info(f"Sezione decompressa: {len(decompressed)} bytes")
                self._parse_packets(decompressed)
            except zlib.error:
                logger.info("Sezione non compressa, parsing diretto")
                self._parse_packets(self.binary_data)
                
        except Exception as e:
            logger.error(f"Errore parsing sezione binaria: {e}")
    
    def _parse_packets(self, data: bytes):
        """Parse avanzato dei packets dalla sezione binaria"""
        
        # Tipi di packet conosciuti (da reverse engineering community)
        KNOWN_PACKET_TYPES = {
            0x00: "position_update",
            0x01: "rotation_update", 
            0x02: "fire_shot",
            0x03: "reload_start",
            0x04: "damage_dealt",
            0x05: "vehicle_spotted",
            0x06: "vehicle_destroyed",
            0x07: "chat_message",
            0x08: "consumable_used",
            0x09: "module_damaged",
            0x0A: "movement_input",  # Possibile input WASD
            0x0B: "mouse_input",    # Possibile input mouse
            0x0C: "key_pressed",    # Possibile input generico
        }
        
        with io.BytesIO(data) as f:
            packet_count = 0
            input_events = []
            
            while f.tell() < len(data) - 12:  # Almeno 12 bytes rimasti
                try:
                    # Leggi header esteso del packet
                    timestamp_raw = struct.unpack('<f', f.read(4))[0]  # Timestamp battaglia
                    packet_type = struct.unpack('<I', f.read(4))[0]
                    packet_size = struct.unpack('<I', f.read(4))[0]
                    
                    # Controllo sanity
                    if packet_size > len(data) or packet_size == 0 or packet_size > 1000000:
                        # Prova formato alternativo
                        f.seek(-8, 1)  # Torna indietro
                        packet_type = struct.unpack('<H', f.read(2))[0]  # Tipo più piccolo
                        packet_size = struct.unpack('<H', f.read(2))[0]  # Size più piccolo
                        
                        if packet_size > 10000 or packet_size == 0:
                            break
                            
                    packet_data = f.read(packet_size)
                    
                    # Identifica tipo di evento
                    event_type = KNOWN_PACKET_TYPES.get(packet_type & 0xFF, f"unknown_{packet_type}")
                    
                    # Parse eventi specifici che potrebbero essere input
                    parsed_data = self._parse_specific_packet(packet_type, packet_data, timestamp_raw)
                    
                    event = BattleEvent(
                        timestamp=timestamp_raw,
                        event_type=event_type,
                        data=parsed_data
                    )
                    
                    self.events.append(event)
                    
                    # Colleziona eventi che sembrano input del giocatore
                    if self._is_likely_player_input(packet_type, parsed_data):
                        input_events.append(event)
                    
                    packet_count += 1
                    
                    if packet_count % 5000 == 0:
                        logger.info(f"Processati {packet_count} packets, trovati {len(input_events)} possibili input...")
                        
                    # Limita per performance in fase di sviluppo
                    if packet_count > 50000:
                        logger.info(f"Limite raggiunto per sviluppo: {packet_count} packets")
                        break
                        
                except (struct.error, OSError):
                    # Prova a trovare il prossimo packet header valido
                    if not self._find_next_packet_header(f):
                        break
                except Exception as e:
                    logger.warning(f"Errore packet {packet_count}: {e}")
                    break
            
            logger.info(f"Totale packets processati: {packet_count}")
            logger.info(f"Possibili input del giocatore trovati: {len(input_events)}")
            
            # Salva eventi input separatamente per analisi
            self.player_input_events = input_events
            
    def _parse_specific_packet(self, packet_type: int, data: bytes, timestamp: float) -> Dict[str, Any]:
        """Parse specifico per diversi tipi di packet"""
        
        if len(data) < 4:
            return {"raw_size": len(data)}
            
        try:
            # Prova a parsare position update (molto comune)
            if (packet_type & 0xFF) == 0x00 and len(data) >= 12:
                x, y, z = struct.unpack('<fff', data[:12])
                return {
                    "type": "position",
                    "x": round(x, 2),
                    "y": round(y, 2), 
                    "z": round(z, 2),
                    "player_controlled": self._is_player_position_update(x, y, z)
                }
                
            # Prova rotation update  
            elif (packet_type & 0xFF) == 0x01 and len(data) >= 8:
                hull_rotation, turret_rotation = struct.unpack('<ff', data[:8])
                return {
                    "type": "rotation",
                    "hull": round(hull_rotation, 3),
                    "turret": round(turret_rotation, 3),
                    "rapid_change": abs(hull_rotation) > 0.1  # Indica input attivo
                }
                
            # Prova fire shot
            elif (packet_type & 0xFF) == 0x02 and len(data) >= 4:
                target_id = struct.unpack('<I', data[:4])[0]
                return {
                    "type": "fire_shot",
                    "target_id": target_id,
                    "is_player_shot": self._is_player_entity(target_id)
                }
                
            # Packet sconosciuto ma con pattern che potrebbe essere input
            else:
                # Cerca pattern che potrebbero indicare input
                input_patterns = self._analyze_for_input_patterns(data)
                return {
                    "type": f"unknown_{packet_type}",
                    "size": len(data),
                    "potential_input": input_patterns,
                    "first_bytes": data[:8].hex() if len(data) >= 8 else data.hex()
                }
                
        except struct.error:
            return {"raw_size": len(data), "parse_error": True}
    
    def _is_likely_player_input(self, packet_type: int, data: Dict) -> bool:
        """Identifica se un packet potrebbe rappresentare input del giocatore"""
        
        # Position updates frequenti indicano movimento attivo
        if data.get("type") == "position" and data.get("player_controlled"):
            return True
            
        # Rotation rapide indicano input mouse/tastiera
        if data.get("type") == "rotation" and data.get("rapid_change"):
            return True
            
        # Fire shots del giocatore
        if data.get("type") == "fire_shot" and data.get("is_player_shot"):
            return True
            
        # Pattern binari che potrebbero essere tasti/mouse
        if data.get("potential_input", {}).get("has_input_pattern"):
            return True
            
        return False
    
    def _analyze_for_input_patterns(self, data: bytes) -> Dict[str, Any]:
        """Analizza pattern binari che potrebbero essere input"""
        
        if len(data) < 2:
            return {"has_input_pattern": False}
            
        patterns = {
            "has_input_pattern": False,
            "binary_flags": [],
            "possible_keys": []
        }
        
        # Cerca pattern di bit che potrebbero essere key states
        for i in range(min(4, len(data))):
            byte_val = data[i]
            # Pattern comuni per WASD (bit flags)
            if byte_val in [1, 2, 4, 8, 16, 32, 64, 128]:  # Single bit flags
                patterns["binary_flags"].append(f"0x{byte_val:02X}")
                patterns["has_input_pattern"] = True
                
            # Combinazioni comuni WASD
            elif byte_val in [3, 5, 6, 9, 10, 12]:  # Multiple bits
                patterns["binary_flags"].append(f"0x{byte_val:02X}")
                patterns["has_input_pattern"] = True
                
        # Cerca pattern di coordinate che potrebbero essere mouse
        if len(data) >= 8:
            try:
                val1, val2 = struct.unpack('<ff', data[:8])
                if -1000 < val1 < 1000 and -1000 < val2 < 1000:
                    patterns["possible_mouse"] = {"x": val1, "y": val2}
                    patterns["has_input_pattern"] = True
            except:
                pass
        
        return patterns
    
    def _find_next_packet_header(self, f: io.BytesIO) -> bool:
        """Cerca il prossimo header di packet valido"""
        start_pos = f.tell()
        
        while f.tell() < start_pos + 1000:  # Cerca nei prossimi 1KB
            try:
                byte = f.read(1)
                if not byte:
                    return False
                    
                # Cerca pattern che potrebbero essere header
                if byte[0] in [0x00, 0x01, 0x02, 0x03, 0x04]:
                    # Verifica se i prossimi bytes sembrano un header valido
                    next_bytes = f.read(7)
                    if len(next_bytes) == 7:
                        # Controlla se sembra un timestamp + type + size valido
                        try:
                            f.seek(-4, 1)
                            return True
                        except:
                            continue
            except:
                return False
        
        return False
    
    def _is_player_position_update(self, x: float, y: float, z: float) -> bool:
        """Determina se un position update appartiene al giocatore del replay"""
        # Logica per identificare il giocatore principale
        # Semplificata per ora - potremmo tracciare le posizioni nel tempo
        return True  # Per ora assumiamo tutti come potenziali
    
    def _is_player_entity(self, entity_id: int) -> bool:
        """Determina se un'entità appartiene al giocatore del replay"""
        # Logica per identificare il giocatore - da migliorare
        return entity_id in [1, 2, 3]  # IDs comuni del giocatore
        
    def get_player_input_timeline(self) -> List[Dict[str, Any]]:
        """Ritorna timeline degli input del giocatore per AI training"""
        if not hasattr(self, 'player_input_events'):
            return []
            
        timeline = []
        for event in self.player_input_events:
            timeline.append({
                "timestamp": event.timestamp,
                "action_type": event.data.get("type", "unknown"),
                "details": {k: v for k, v in event.data.items() 
                           if k not in ["raw_size", "first_bytes"]},
                "confidence": self._calculate_input_confidence(event)
            })
            
        return sorted(timeline, key=lambda x: x["timestamp"])
    
    def _calculate_input_confidence(self, event: BattleEvent) -> float:
        """Calcola quanto è probabile che sia un vero input del giocatore"""
        confidence = 0.5
        
        if event.data.get("type") == "position":
            confidence += 0.2
        if event.data.get("player_controlled"):
            confidence += 0.2
        if event.data.get("rapid_change"):
            confidence += 0.3
        if event.data.get("has_input_pattern"):
            confidence += 0.3
            
        return min(1.0, confidence)
    
    def _get_input_summary(self) -> Dict[str, Any]:
        """Riassunto statistico degli input del giocatore"""
        if not hasattr(self, 'player_input_events'):
            return {"total_inputs": 0}
            
        input_types = {}
        total_inputs = len(self.player_input_events)
        
        for event in self.player_input_events:
            event_type = event.data.get("type", "unknown")
            input_types[event_type] = input_types.get(event_type, 0) + 1
            
        return {
            "total_inputs": total_inputs,
            "input_types": input_types,
            "inputs_per_minute": total_inputs / max(1, len(self.events) * 0.1 / 60),
            "most_common_input": max(input_types.items(), key=lambda x: x[1])[0] if input_types else "none"
        }
    
    def _analyze_movement_patterns(self) -> Dict[str, Any]:
        """Analizza pattern di movimento del giocatore"""
        if not hasattr(self, 'player_input_events'):
            return {}
            
        movement_events = [e for e in self.player_input_events 
                          if e.data.get("type") == "position"]
        
        if len(movement_events) < 2:
            return {"insufficient_data": True}
            
        # Calcola velocità e pattern di movimento
        speeds = []
        direction_changes = 0
        last_pos = None
        
        for event in movement_events:
            if "x" in event.data and "y" in event.data:
                current_pos = (event.data["x"], event.data["y"])
                
                if last_pos:
                    # Calcola distanza e velocità
                    distance = ((current_pos[0] - last_pos[0])**2 + 
                               (current_pos[1] - last_pos[1])**2)**0.5
                    time_diff = max(0.1, event.timestamp - prev_timestamp)
                    speed = distance / time_diff
                    speeds.append(speed)
                    
                last_pos = current_pos
                prev_timestamp = event.timestamp
        
        return {
            "total_movements": len(movement_events),
            "avg_speed": sum(speeds) / len(speeds) if speeds else 0,
            "max_speed": max(speeds) if speeds else 0,
            "movement_frequency": len(movement_events) / max(1, len(self.events) * 0.1 / 60),
            "speed_variance": self._calculate_variance(speeds) if len(speeds) > 1 else 0
        }
    
    def _analyze_combat_actions(self) -> Dict[str, Any]:
        """Analizza azioni di combattimento del giocatore"""
        if not hasattr(self, 'player_input_events'):
            return {}
            
        fire_events = [e for e in self.player_input_events 
                      if e.data.get("type") == "fire_shot"]
        
        rotation_events = [e for e in self.player_input_events 
                          if e.data.get("type") == "rotation"]
        
        return {
            "total_shots": len(fire_events),
            "shots_per_minute": len(fire_events) / max(1, len(self.events) * 0.1 / 60),
            "rotation_actions": len(rotation_events),
            "rapid_rotations": len([e for e in rotation_events 
                                  if e.data.get("rapid_change")]),
            "combat_intensity": (len(fire_events) + len(rotation_events)) / max(1, len(self.player_input_events)),
            "avg_time_between_shots": self._calculate_avg_time_between_events(fire_events)
        }
    
    def _calculate_variance(self, values: List[float]) -> float:
        """Calcola varianza di una lista di valori"""
        if len(values) < 2:
            return 0
        mean = sum(values) / len(values)
        return sum((x - mean) ** 2 for x in values) / len(values)
    
    def _calculate_avg_time_between_events(self, events: List[BattleEvent]) -> float:
        """Calcola tempo medio tra eventi"""
        if len(events) < 2:
            return 0
            
        time_diffs = []
        for i in range(1, len(events)):
            time_diffs.append(events[i].timestamp - events[i-1].timestamp)
            
        return sum(time_diffs) / len(time_diffs) if time_diffs else 0
    
    def get_summary(self) -> Dict[str, Any]:
        """Ritorna riassunto del replay per AI"""
        summary = {
            "metadata": {
                "version": self.metadata.replay_version if self.metadata else "unknown",
                "map": self.metadata.map_name if self.metadata else "unknown", 
                "mode": self.metadata.battle_mode if self.metadata else "unknown",
                "player": self.metadata.player_name if self.metadata else "unknown",
                "tank": self.metadata.player_tank if self.metadata else "unknown",
                "arena_id": self.metadata.arena_unique_id if self.metadata else 0,
            },
            "players": {
                "count": len(self.players),
                "teams": len(set(p.team for p in self.players.values())),
                "players": [
                    {
                        "name": p.name,
                        "clan": p.clan,
                        "tank": p.tank,
                        "tier": p.tier,
                        "team": p.team
                    }
                    for p in self.players.values()
                ]
            },
            "events": {
                "count": len(self.events),
                "types": list(set(e.event_type for e in self.events))
            }
        }
        
        return summary
    
    def export_for_ai_training(self) -> Dict[str, Any]:
        """Esporta dati in formato ottimale per training AI"""
        
        def make_json_serializable(obj):
            """Converte oggetti non serializzabili in JSON"""
            if isinstance(obj, bytes):
                return obj.hex()  # Converte bytes in hex string
            elif isinstance(obj, dict):
                return {k: make_json_serializable(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [make_json_serializable(item) for item in obj]
            else:
                return obj
        
        # Estrai statistiche dettagliate dai veicoli
        team_stats = {}
        for team in set(p.team for p in self.players.values()):
            team_vehicles = [p for p in self.players.values() if p.team == team]
            team_stats[team] = {
                "vehicles": [{"tank": p.tank, "tier": p.tier, "player": p.name, "clan": p.clan} 
                           for p in team_vehicles],
                "total_vehicles": len(team_vehicles),
                "tiers": [p.tier for p in team_vehicles if p.tier > 0],
                "tank_classes": self._categorize_tanks([p.tank for p in team_vehicles])
            }
        
        return {
            "battle_info": {
                "map": self.metadata.map_name if self.metadata else "unknown",
                "mode": str(self.metadata.battle_mode) if self.metadata else "unknown",
                "player": self.metadata.player_name if self.metadata else "unknown",
                "player_tank": self.metadata.player_tank if self.metadata else "unknown",
                "replay_version": self.metadata.replay_version if self.metadata else "unknown",
                "duration_estimate": len(self.events) * 0.1,  # Durata approssimata
                "arena_id": self.metadata.arena_unique_id if self.metadata else 0,
            },
            "team_composition": team_stats,
            "battle_statistics": {
                "total_players": len(self.players),
                "teams": len(set(p.team for p in self.players.values())),
                "clans_present": len(set(p.clan for p in self.players.values() if p.clan)),
                "nations": list(set(p.tank.split(':')[0] if ':' in p.tank else 'unknown' 
                                  for p in self.players.values())),
            },
            "events_summary": {
                "total_events": len(self.events),
                "event_types": list(set(e.event_type for e in self.events)),
                "binary_data_size": len(self.binary_data),
                "first_events": [
                    {
                        "time": e.timestamp,
                        "type": e.event_type,
                        "data_size": len(str(e.data)) if e.data else 0,
                        # Serializza solo dati sicuri
                        "data": make_json_serializable(
                            {k: v for k, v in e.data.items() if k != 'raw'}
                        ) if isinstance(e.data, dict) else str(e.data)[:100]
                    }
                    for e in self.events[:10]  # Solo primi 10 per sicurezza
                ]
            },
            "player_inputs": {
                "input_timeline": self.get_player_input_timeline()[:50],  # Primi 50 input
                "input_summary": self._get_input_summary(),
                "movement_patterns": self._analyze_movement_patterns(),
                "combat_actions": self._analyze_combat_actions()
            }
        }
    
    def _categorize_tanks(self, tank_list: List[str]) -> Dict[str, int]:
        """Categorizza i carri per classe (approssimativa)"""
        categories = {
            "heavy": 0,
            "medium": 0, 
            "light": 0,
            "tank_destroyer": 0,
            "artillery": 0,
            "unknown": 0
        }
        
        for tank in tank_list:
            tank_lower = tank.lower()
            if any(x in tank_lower for x in ['kv', 'is-', 'tiger', 'churchill', 't29', 't32']):
                categories["heavy"] += 1
            elif any(x in tank_lower for x in ['t-34', 'm4', 'panzer', 'sherman', 'cromwell']):
                categories["medium"] += 1
            elif any(x in tank_lower for x in ['chaffee', 'luchs', 'bt-', 'elc', 't37']):
                categories["light"] += 1
            elif any(x in tank_lower for x in ['stug', 'jagd', 'su-', 'hellcat', 'at-']):
                categories["tank_destroyer"] += 1
            elif any(x in tank_lower for x in ['lefh', 'su-26', 'wespe', 'bishop', 'fv304']):
                categories["artillery"] += 1
            else:
                categories["unknown"] += 1
                
        return categories

# Utility function per batch processing
def parse_multiple_replays(replay_dir: str, output_dir: str = None):
    """Parse multipli replay e salva risultati"""
    replay_path = Path(replay_dir)
    results = []
    
    replay_files = list(replay_path.glob("*.wotreplay"))
    logger.info(f"Trovati {len(replay_files)} replay files")
    
    for replay_file in replay_files:
        parser = WoTReplayParser(str(replay_file))
        
        if parser.parse():
            summary = parser.get_summary()
            summary["filename"] = replay_file.name
            results.append(summary)
            
            # Salva individualmente se richiesto
            if output_dir:
                output_path = Path(output_dir)
                output_path.mkdir(exist_ok=True)
                
                json_file = output_path / f"{replay_file.stem}.json"
                with open(json_file, 'w') as f:
                    json.dump(parser.export_for_ai_training(), f, indent=2)
        
        else:
            logger.error(f"Fallito parsing di {replay_file}")
    
    logger.info(f"Parsing completato: {len(results)}/{len(replay_files)} successi")
    return results

# Utility function per creare file di test
def create_test_replay(filename: str = "test_replay.wotreplay"):
    """Crea un replay di test per verificare il parser"""
    
    # Dati di test simulati
    test_data = {
        "clientVersionFromExe": "1.23.1.0",
        "mapDisplayName": "Himmelsdorf",
        "battleType": "random",
        "gameplayID": "ctf",
        "playerName": "TestPlayer",
        "playerVehicle": "germany-Pz_VI_Tiger_I",
        "dateTime": "24.08.2025 14:30:45",
        "arenaUniqueID": 12345678,
        "vehicles": {
            "1": {
                "name": "TestPlayer",
                "clanAbbrev": "TEST",
                "vehicleType": "germany-Pz_VI_Tiger_I",
                "level": 7,
                "team": 1
            },
            "2": {
                "name": "Enemy1", 
                "clanAbbrev": "FOE",
                "vehicleType": "ussr-T-34-85",
                "level": 6,
                "team": 2
            }
        }
    }
    
    # Crea file con formato corretto
    with open(filename, 'wb') as f:
        # Magic number
        f.write(struct.pack('<I', WoTReplayParser.MAGIC_NUMBER))
        
        # Un blocco JSON
        json_data = json.dumps(test_data).encode('utf-8')
        f.write(struct.pack('<I', 1))  # 1 blocco
        f.write(struct.pack('<I', len(json_data)))  # Dimensione blocco
        f.write(json_data)
        
        # Sezione binaria fake (alcuni packet di test)
        binary_data = b""
        for i in range(10):
            packet = struct.pack('<II', i, 16) + b"test_packet_data"
            binary_data += packet
            
        f.write(binary_data)
    
    logger.info(f"File di test creato: {filename}")
    return filename

# Esempio di uso
if __name__ == "__main__":
    import sys
    
    replay_file = "example.wotreplay"
    
    # Se il file non esiste, crea uno di test
    if not Path(replay_file).exists():
        logger.info("File example.wotreplay non trovato, creo file di test")
        replay_file = create_test_replay(replay_file)
    
    # Test singolo replay
    parser = WoTReplayParser(replay_file)
    
    if parser.parse():
        summary = parser.get_summary()
        print("=== RIASSUNTO REPLAY ===")
        print(json.dumps(summary, indent=2))
        
        # Mostra input del giocatore se trovati
        if hasattr(parser, 'player_input_events') and parser.player_input_events:
            print(f"\n=== INPUT DEL GIOCATORE TROVATI: {len(parser.player_input_events)} ===")
            
            input_timeline = parser.get_player_input_timeline()
            for i, input_event in enumerate(input_timeline[:10]):  # Primi 10
                print(f"{i+1:2d}. [t={input_event['timestamp']:6.1f}s] "
                     f"{input_event['action_type']:15s} "
                     f"(conf: {input_event['confidence']:.2f}) "
                     f"- {input_event['details']}")
            
            if len(input_timeline) > 10:
                print(f"... e altri {len(input_timeline) - 10} input")
        
        # Esporta per AI training
        ai_data = parser.export_for_ai_training()
        print("\n=== DATI PER AI ===")
        print(json.dumps(ai_data, indent=2))
        
        # Salva in file per analisi dettagliata
        with open("replay_analysis.json", "w") as f:
            json.dump(ai_data, f, indent=2)
        print("\n✅ Dati salvati in 'replay_analysis.json' per analisi dettagliata!")
        
    else:
        print("Parsing fallito - controlla i log per dettagli")
    
    # Test batch processing
    # results = parse_multiple_replays("./replays", "./parsed_data")