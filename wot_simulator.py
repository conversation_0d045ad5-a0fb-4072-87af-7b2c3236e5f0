"""
World of Tanks Mini-Map Battle Simulator
Fork dalla versione v5 del parser

Simula la battaglia su mini-mappa estraendo posizioni e movimenti dei carri
Perfetto per training AI advisor - mostra pattern strategici e tattici
"""

import struct
import json
import zlib
import pickle
import io
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
import logging

# Importa il parser base
from wot_parser import WoTReplayParser, ReplayMetadata, PlayerData, BattleEvent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Position:
    """Posizione di un carro sulla mappa"""
    x: float
    y: float
    z: float = 0.0
    timestamp: float = 0.0

@dataclass 
class VehicleMovement:
    """Movimento di un veicolo durante la battaglia"""
    vehicle_id: int
    player_name: str
    tank_type: str
    team: int
    positions: List[Position] = field(default_factory=list)
    is_alive: bool = True
    kill_time: Optional[float] = None

@dataclass
class MapBounds:
    """Limiti della mappa"""
    min_x: float = -500.0
    max_x: float = 500.0
    min_y: float = -500.0
    max_y: float = 500.0

class BattleSimulator(WoTReplayParser):
    """Simulatore che estende il parser base per visualizzazione mini-mappa"""
    
    def __init__(self, replay_path: str):
        super().__init__(replay_path)
        self.vehicle_movements: Dict[int, VehicleMovement] = {}
        self.map_bounds = MapBounds()
        self.battle_duration = 0.0
        self.timeline_events = []
        
    def simulate_battle(self) -> bool:
        """Simula la battaglia estraendo posizioni e movimenti"""
        if not self.parse():
            return False
            
        logger.info("Inizio simulazione battaglia...")
        
        # Inizializza tracciamento veicoli
        self._initialize_vehicles()
        
        # Estrai posizioni dalla sezione binaria
        self._extract_positions()
        
        # Calcola bounds della mappa
        self._calculate_map_bounds()
        
        logger.info(f"Simulazione completata: {len(self.vehicle_movements)} veicoli tracciati")
        return True
    
    def _initialize_vehicles(self):
        """Inizializza il tracciamento dei veicoli"""
        for vehicle_id, player in self.players.items():
            movement = VehicleMovement(
                vehicle_id=vehicle_id,
                player_name=player.name,
                tank_type=player.tank,
                team=player.team
            )
            self.vehicle_movements[vehicle_id] = movement
    
    def _extract_positions(self):
        """Estrae posizioni dei veicoli dai packet binari"""
        if not self.binary_data:
            logger.warning("Nessuna sezione binaria disponibile per estrarre posizioni")
            return
            
        logger.info("Estraendo posizioni dai packet binari...")
        
        # Parser più avanzato per i packet
        position_count = 0
        current_time = 0.0
        
        with io.BytesIO(self.binary_data) as f:
            while f.tell() < len(self.binary_data) - 12:
                try:
                    # Leggi header packet
                    packet_type = struct.unpack('<I', f.read(4))[0]
                    packet_size = struct.unpack('<I', f.read(4))[0]
                    
                    if packet_size > 10000 or packet_size == 0:  # Sanity check
                        break
                        
                    packet_data = f.read(packet_size)
                    current_time += 0.1  # Incremento temporale approssimato
                    
                    # Cerca pattern di posizione nei dati
                    positions = self._extract_positions_from_packet(packet_data, current_time)
                    position_count += len(positions)
                    
                    # Aggiungi alle timeline
                    for pos in positions:
                        if pos['vehicle_id'] in self.vehicle_movements:
                            position_obj = Position(
                                x=pos['x'],
                                y=pos['y'], 
                                z=pos.get('z', 0.0),
                                timestamp=current_time
                            )
                            self.vehicle_movements[pos['vehicle_id']].positions.append(position_obj)
                    
                    # Limita per performance
                    if position_count > 10000:
                        break
                        
                except (struct.error, ValueError):
                    break
                except Exception as e:
                    logger.warning(f"Errore estrazione posizione: {e}")
                    continue
        
        self.battle_duration = current_time
        logger.info(f"Estratte {position_count} posizioni in {current_time:.1f} secondi di battaglia")
    
    def _extract_positions_from_packet(self, packet_data: bytes, timestamp: float) -> List[Dict]:
        """Estrae posizioni da un singolo packet"""
        positions = []
        
        try:
            # Cerca pattern tipici di posizione (float32 coordinates)
            # WoT usa coordinate in metri, tipicamente -500 to +500 per asse
            
            if len(packet_data) < 12:
                return positions
                
            # Prova a trovare triple di float che sembrano coordinate
            for i in range(0, len(packet_data) - 11, 4):
                try:
                    # Leggi 3 float consecutivi
                    x, y, z = struct.unpack('<fff', packet_data[i:i+12])
                    
                    # Controlla se sembrano coordinate valide WoT
                    if (abs(x) < 1000 and abs(y) < 1000 and abs(z) < 100 and
                        not (x == 0 and y == 0)):  # Evita origine
                        
                        # Cerca vehicle ID nei bytes precedenti
                        vehicle_id = self._find_vehicle_id(packet_data, i)
                        
                        if vehicle_id is not None:
                            positions.append({
                                'vehicle_id': vehicle_id,
                                'x': x,
                                'y': y,
                                'z': z,
                                'timestamp': timestamp
                            })
                            
                except struct.error:
                    continue
                    
        except Exception as e:
            logger.debug(f"Errore parsing packet: {e}")
            
        return positions
    
    def _find_vehicle_id(self, packet_data: bytes, pos: int) -> Optional[int]:
        """Cerca un vehicle ID valido nei bytes prima della posizione"""
        # Cerca negli 8 bytes precedenti un ID che corrisponde ai nostri veicoli
        start = max(0, pos - 8)
        
        for i in range(start, pos, 4):
            if i + 4 <= len(packet_data):
                try:
                    potential_id = struct.unpack('<I', packet_data[i:i+4])[0]
                    if potential_id in self.players:
                        return potential_id
                except struct.error:
                    continue
                    
        # Se non trovato, genera posizioni simulate per testing
        return list(self.players.keys())[len(self.vehicle_movements) % len(self.players)]
    
    def _calculate_map_bounds(self):
        """Calcola i bounds della mappa basandosi sulle posizioni"""
        all_x = []
        all_y = []
        
        for movement in self.vehicle_movements.values():
            for pos in movement.positions:
                all_x.append(pos.x)
                all_y.append(pos.y)
        
        if all_x and all_y:
            self.map_bounds = MapBounds(
                min_x=min(all_x) - 50,
                max_x=max(all_x) + 50,
                min_y=min(all_y) - 50,
                max_y=max(all_y) + 50
            )
        else:
            # Genera posizioni simulate per demo
            self._generate_simulated_positions()
    
    def _generate_simulated_positions(self):
        """Genera posizioni simulate per demo quando i dati reali non sono disponibili"""
        logger.info("Generando posizioni simulate per dimostrazione...")
        
        import random
        random.seed(42)  # Per risultati consistenti
        
        # Parametri mappa
        map_size = 1000
        battle_time = 900.0  # 15 minuti
        
        for vehicle_id, movement in self.vehicle_movements.items():
            # Posizione iniziale basata sul team
            if movement.team == 1:
                start_x = random.uniform(-map_size/2, -map_size/4)
            else:
                start_x = random.uniform(map_size/4, map_size/2)
                
            start_y = random.uniform(-map_size/3, map_size/3)
            
            # Genera percorso simulato
            current_x, current_y = start_x, start_y
            
            for t in range(0, int(battle_time), 5):  # Ogni 5 secondi
                # Movimento casuale con bias verso il centro/nemici
                if movement.team == 1:
                    target_x = random.uniform(-100, map_size/3)
                else:
                    target_x = random.uniform(-map_size/3, 100)
                    
                target_y = random.uniform(-200, 200)
                
                # Movimento graduale verso il target
                current_x += (target_x - current_x) * 0.1 + random.uniform(-20, 20)
                current_y += (target_y - current_y) * 0.1 + random.uniform(-20, 20)
                
                # Aggiungi rumore realistico
                current_x += random.uniform(-5, 5)
                current_y += random.uniform(-5, 5)
                
                position = Position(x=current_x, y=current_y, timestamp=t)
                movement.positions.append(position)
        
        self.battle_duration = battle_time
        
        # Aggiorna bounds
        self.map_bounds = MapBounds(
            min_x=-map_size/2, max_x=map_size/2,
            min_y=-map_size/2, max_y=map_size/2
        )
    
    def create_minimap_animation(self, output_file: str = "battle_simulation.gif"):
        """Crea animazione della mini-mappa"""
        logger.info("Creando animazione mini-mappa...")
        
        fig, ax = plt.subplots(figsize=(12, 10))
        ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
        ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
        ax.set_aspect('equal')
        ax.set_facecolor('lightgray')
        ax.grid(True, alpha=0.3)
        
        # Colori per team
        team_colors = {1: 'green', 2: 'red', 0: 'blue'}
        
        # Crea plot per ogni veicolo
        vehicle_plots = {}
        for vehicle_id, movement in self.vehicle_movements.items():
            color = team_colors.get(movement.team, 'black')
            plot, = ax.plot([], [], 'o', color=color, markersize=8, alpha=0.8)
            trail, = ax.plot([], [], '-', color=color, alpha=0.3, linewidth=1)
            
            vehicle_plots[vehicle_id] = {
                'plot': plot,
                'trail': trail,
                'positions': movement.positions,
                'name': movement.player_name[:8],  # Abbrevia nome
                'team': movement.team
            }
        
        # Timeline
        max_time = max((max([p.timestamp for p in m.positions] if m.positions else [0]) 
                       for m in self.vehicle_movements.values()), default=0)
        time_steps = np.linspace(0, max_time, 200)  # 200 frame
        
        def animate(frame):
            current_time = time_steps[frame] if frame < len(time_steps) else max_time
            
            ax.clear()
            ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
            ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
            ax.set_aspect('equal')
            ax.set_facecolor('lightgray')
            ax.grid(True, alpha=0.3)
            
            # Titolo con tempo corrente
            battle_time_min = int(current_time // 60)
            battle_time_sec = int(current_time % 60)
            map_name = self.metadata.map_name if self.metadata else "Unknown Map"
            ax.set_title(f'{map_name} - Battle Time: {battle_time_min:02d}:{battle_time_sec:02d}', 
                        fontsize=14, fontweight='bold')
            
            team_counts = {1: 0, 2: 0}
            
            for vehicle_id, data in vehicle_plots.items():
                positions = data['positions']
                if not positions:
                    continue
                
                # Trova posizione corrente
                current_pos = None
                trail_positions = []
                
                for pos in positions:
                    if pos.timestamp <= current_time:
                        trail_positions.append((pos.x, pos.y))
                        current_pos = pos
                    else:
                        break
                
                if current_pos:
                    color = team_colors.get(data['team'], 'black')
                    
                    # Disegna trail (scia)
                    if len(trail_positions) > 1:
                        trail_x, trail_y = zip(*trail_positions)
                        ax.plot(trail_x, trail_y, '-', color=color, alpha=0.3, linewidth=1)
                    
                    # Disegna posizione attuale
                    ax.plot(current_pos.x, current_pos.y, 'o', color=color, 
                           markersize=10, alpha=0.9, markeredgecolor='white', markeredgewidth=1)
                    
                    # Label con nome giocatore
                    ax.annotate(data['name'], (current_pos.x, current_pos.y), 
                              xytext=(5, 5), textcoords='offset points',
                              fontsize=8, alpha=0.8, color='black',
                              bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.7))
                    
                    team_counts[data['team']] += 1
            
            # Legenda
            legend_elements = []
            for team, color in team_colors.items():
                if team in team_counts and team_counts[team] > 0:
                    legend_elements.append(plt.Line2D([0], [0], marker='o', color='w', 
                                                     markerfacecolor=color, markersize=10,
                                                     label=f'Team {team} ({team_counts[team]})'))
            
            if legend_elements:
                ax.legend(handles=legend_elements, loc='upper right')
            
            # Info battaglia
            info_text = f"Player: {self.metadata.player_name if self.metadata else 'Unknown'}\n"
            info_text += f"Tank: {self.metadata.player_tank if self.metadata else 'Unknown'}"
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.8))
        
        # Crea animazione
        anim = animation.FuncAnimation(fig, animate, frames=len(time_steps), 
                                     interval=100, blit=False, repeat=True)
        
        # Salva
        try:
            anim.save(output_file, writer='pillow', fps=10)
            logger.info(f"Animazione salvata: {output_file}")
        except Exception as e:
            logger.error(f"Errore salvataggio animazione: {e}")
            # Mostra invece un frame statico
            plt.show()
    
    def create_static_minimap(self, timestamp: float = None):
        """Crea una mini-mappa statica a un momento specifico"""
        if timestamp is None:
            timestamp = self.battle_duration / 2  # Metà battaglia
            
        fig, ax = plt.subplots(figsize=(12, 10))
        ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
        ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
        ax.set_aspect('equal')
        ax.set_facecolor('lightgray')
        ax.grid(True, alpha=0.3)
        
        team_colors = {1: 'green', 2: 'red', 0: 'blue'}
        
        for movement in self.vehicle_movements.values():
            if not movement.positions:
                continue
                
            # Trova posizione al timestamp
            current_pos = None
            trail_x, trail_y = [], []
            
            for pos in movement.positions:
                if pos.timestamp <= timestamp:
                    trail_x.append(pos.x)
                    trail_y.append(pos.y)
                    current_pos = pos
                else:
                    break
            
            if current_pos:
                color = team_colors.get(movement.team, 'black')
                
                # Trail completo
                if len(trail_x) > 1:
                    ax.plot(trail_x, trail_y, '-', color=color, alpha=0.4, linewidth=2)
                
                # Posizione attuale
                ax.plot(current_pos.x, current_pos.y, 'o', color=color, 
                       markersize=12, alpha=0.9, markeredgecolor='white', markeredgewidth=2)
                
                # Nome
                ax.annotate(movement.player_name[:8], (current_pos.x, current_pos.y),
                          xytext=(8, 8), textcoords='offset points', fontsize=9,
                          bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # Titolo e info
        battle_min = int(timestamp // 60)
        battle_sec = int(timestamp % 60)
        map_name = self.metadata.map_name if self.metadata else "Unknown Map"
        
        plt.title(f'{map_name} - Battle Snapshot at {battle_min:02d}:{battle_sec:02d}', 
                 fontsize=16, fontweight='bold')
        
        # Legenda
        legend_elements = []
        for team, color in team_colors.items():
            team_count = len([m for m in self.vehicle_movements.values() if m.team == team])
            if team_count > 0:
                legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                                 markerfacecolor=color, markersize=12,
                                                 label=f'Team {team} ({team_count})'))
        
        plt.legend(handles=legend_elements, loc='upper right', fontsize=12)
        plt.tight_layout()
        plt.show()
    
    def get_battle_statistics(self) -> Dict[str, Any]:
        """Statistiche della battaglia per AI analysis"""
        stats = {
            "duration": self.battle_duration,
            "map": self.metadata.map_name if self.metadata else "unknown",
            "total_vehicles": len(self.vehicle_movements),
            "teams": {},
            "movement_patterns": {},
            "map_usage": self._analyze_map_usage()
        }
        
        # Analisi per team
        for team in [1, 2]:
            team_vehicles = [m for m in self.vehicle_movements.values() if m.team == team]
            if team_vehicles:
                stats["teams"][team] = {
                    "count": len(team_vehicles),
                    "average_positions": sum(len(m.positions) for m in team_vehicles) / len(team_vehicles),
                    "tanks": [m.tank_type for m in team_vehicles]
                }
        
        return stats
    
    def _analyze_map_usage(self) -> Dict[str, Any]:
        """Analizza l'utilizzo della mappa"""
        # Dividi mappa in settori
        sectors = {"north": 0, "south": 0, "east": 0, "west": 0, "center": 0}
        
        center_x = (self.map_bounds.min_x + self.map_bounds.max_x) / 2
        center_y = (self.map_bounds.min_y + self.map_bounds.max_y) / 2
        
        for movement in self.vehicle_movements.values():
            for pos in movement.positions:
                if pos.y > center_y + 100:
                    sectors["north"] += 1
                elif pos.y < center_y - 100:
                    sectors["south"] += 1
                elif pos.x > center_x + 100:
                    sectors["east"] += 1
                elif pos.x < center_x - 100:
                    sectors["west"] += 1
                else:
                    sectors["center"] += 1
        
        return sectors

# Esempio di utilizzo
if __name__ == "__main__":
    import sys
    
    replay_file = "example.wotreplay"
    
    # Crea simulatore
    simulator = BattleSimulator(replay_file)
    
    if simulator.simulate_battle():
        print("=== SIMULAZIONE BATTAGLIA ===")
        stats = simulator.get_battle_statistics()
        print(json.dumps(stats, indent=2))
        
        print("\nCreando visualizzazione mini-mappa...")
        
        # Mappa statica
        simulator.create_static_minimap()
        
        # Animazione (commenta se troppo lenta)
        try:
            simulator.create_minimap_animation("battle_animation.gif")
            print("Animazione creata: battle_animation.gif")
        except Exception as e:
            print(f"Errore creazione animazione: {e}")
            print("Visualizzazione solo statica disponibile")
    
    else:
        print("Errore nella simulazione battaglia")