"""
World of Tanks Mini-Map Battle Simulator
Fork dalla versione v5 del parser

Simula la battaglia su mini-mappa estraendo posizioni e movimenti dei carri
Perfetto per training AI advisor - mostra pattern strategici e tattici
"""

import struct
import json
import zlib
import pickle
import io
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime
import logging

# Importa il parser base
from wot_parser import WoTReplayParser, ReplayMetadata, PlayerData, BattleEvent

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class Position:
    """Posizione di un carro sulla mappa"""
    x: float
    y: float
    z: float = 0.0
    timestamp: float = 0.0

@dataclass 
class VehicleMovement:
    """Movimento di un veicolo durante la battaglia"""
    vehicle_id: int
    player_name: str
    tank_type: str
    team: int
    positions: List[Position] = field(default_factory=list)
    is_alive: bool = True
    kill_time: Optional[float] = None

@dataclass
class MapBounds:
    """Limiti della mappa"""
    min_x: float = -500.0
    max_x: float = 500.0
    min_y: float = -500.0
    max_y: float = 500.0

class BattleSimulator(WoTReplayParser):
    """Simulatore che estende il parser base per visualizzazione mini-mappa"""
    
    def __init__(self, replay_path: str):
        super().__init__(replay_path)
        self.vehicle_movements: Dict[int, VehicleMovement] = {}
        self.map_bounds = MapBounds()
        self.battle_duration = 0.0
        self.timeline_events = []
        
    def simulate_battle(self) -> bool:
        """Simula la battaglia estraendo posizioni e movimenti"""
        if not self.parse():
            return False
            
        logger.info("Inizio simulazione battaglia...")
        
        # Inizializza tracciamento veicoli
        self._initialize_vehicles()
        
        # Estrai posizioni dalla sezione binaria
        self._extract_positions()
        
        # Calcola bounds della mappa
        self._calculate_map_bounds()
        
        logger.info(f"Simulazione completata: {len(self.vehicle_movements)} veicoli tracciati")
        return True
    
    def _initialize_vehicles(self):
        """Inizializza il tracciamento dei veicoli"""
        for vehicle_id, player in self.players.items():
            movement = VehicleMovement(
                vehicle_id=vehicle_id,
                player_name=player.name,
                tank_type=player.tank,
                team=player.team
            )
            self.vehicle_movements[vehicle_id] = movement
    
    def _extract_positions(self):
        """Estrae posizioni dei veicoli dai packet binari con parsing migliorato"""
        if not self.binary_data:
            logger.warning("Nessuna sezione binaria disponibile per estrarre posizioni")
            return

        logger.info("Estraendo posizioni dai packet binari con parser avanzato...")

        position_count = 0
        packet_count = 0
        current_time = 0.0

        with io.BytesIO(self.binary_data) as f:
            while f.tell() < len(self.binary_data) - 8:
                try:
                    # Leggi timestamp del packet (4 bytes)
                    timestamp_raw = struct.unpack('<I', f.read(4))[0]
                    current_time = timestamp_raw / 1000.0  # Converti in secondi

                    # Leggi dimensione del packet (4 bytes)
                    packet_size = struct.unpack('<I', f.read(4))[0]

                    # Sanity check sulla dimensione
                    if packet_size > 50000 or packet_size == 0:
                        logger.debug(f"Packet size sospetto: {packet_size}, skipping")
                        break

                    # Leggi i dati del packet
                    if f.tell() + packet_size > len(self.binary_data):
                        logger.debug("Packet size eccede i dati disponibili")
                        break

                    packet_data = f.read(packet_size)
                    packet_count += 1

                    # Estrai posizioni dal packet
                    positions = self._extract_positions_from_packet(packet_data, current_time)

                    # Estrai eventi di battaglia dal packet
                    events = self._extract_battle_events(packet_data, current_time)

                    if positions:
                        position_count += len(positions)
                        logger.debug(f"Packet {packet_count}: trovate {len(positions)} posizioni a t={current_time:.1f}s")

                    if events:
                        logger.debug(f"Packet {packet_count}: trovati {len(events)} eventi a t={current_time:.1f}s")
                        self.timeline_events.extend(events)

                    # Aggiungi posizioni alle timeline
                    for pos in positions:
                        if pos['vehicle_id'] in self.vehicle_movements:
                            position_obj = Position(
                                x=pos['x'],
                                y=pos['y'],
                                z=pos.get('z', 0.0),
                                timestamp=current_time
                            )
                            self.vehicle_movements[pos['vehicle_id']].positions.append(position_obj)

                    # Limita per performance e debug
                    if packet_count > 5000:  # Limita numero di packet processati
                        logger.info(f"Raggiunto limite di {packet_count} packet processati")
                        break

                except (struct.error, ValueError) as e:
                    logger.debug(f"Errore struct: {e}")
                    # Prova a continuare dal prossimo byte
                    try:
                        f.seek(f.tell() - 7)  # Torna indietro e prova il prossimo byte
                    except:
                        break
                except Exception as e:
                    logger.warning(f"Errore estrazione posizione: {e}")
                    break

        self.battle_duration = max(current_time, 300.0)  # Minimo 5 minuti
        logger.info(f"Processati {packet_count} packet, estratte {position_count} posizioni reali")
        logger.info(f"Durata battaglia: {self.battle_duration:.1f} secondi")

        # Se non abbiamo estratto abbastanza posizioni reali, genera dati simulati
        if position_count < 100:
            logger.info("Poche posizioni reali estratte, integrando con dati simulati...")
            self._generate_simulated_positions()
    
    def _extract_positions_from_packet(self, packet_data: bytes, timestamp: float) -> List[Dict]:
        """Estrae posizioni da un singolo packet usando il formato WoT corretto"""
        positions = []

        try:
            if len(packet_data) < 8:
                return positions

            # Cerca packet type 0x0A (10) che indica position packets
            packet_type = None
            if len(packet_data) >= 4:
                packet_type = struct.unpack('<I', packet_data[:4])[0]

            # Se è un position packet (type 0x0A = 10)
            if packet_type == 0x0A or packet_type == 10:
                return self._parse_position_packet(packet_data, timestamp)

            # Altrimenti cerca pattern di posizione generici
            return self._search_position_patterns(packet_data, timestamp)

        except Exception as e:
            logger.debug(f"Errore parsing packet: {e}")

        return positions

    def _parse_position_packet(self, packet_data: bytes, timestamp: float) -> List[Dict]:
        """Parsa un packet di posizione specifico (type 0x0A)"""
        positions = []

        try:
            # Skip packet type (4 bytes) e cerca il payload
            offset = 4

            while offset + 16 <= len(packet_data):  # Minimo per vehicle_id + x,y,z
                try:
                    # Leggi vehicle ID (4 bytes)
                    vehicle_id = struct.unpack('<I', packet_data[offset:offset+4])[0]
                    offset += 4

                    # Verifica che sia un vehicle ID valido
                    if vehicle_id not in self.players:
                        offset += 4  # Prova il prossimo offset
                        continue

                    # Leggi coordinate (3 float32)
                    if offset + 12 <= len(packet_data):
                        x, y, z = struct.unpack('<fff', packet_data[offset:offset+12])
                        offset += 12

                        # Valida coordinate
                        if (abs(x) < 2000 and abs(y) < 2000 and abs(z) < 200):
                            positions.append({
                                'vehicle_id': vehicle_id,
                                'x': x,
                                'y': y,
                                'z': z,
                                'timestamp': timestamp
                            })
                            logger.debug(f"Posizione estratta: Vehicle {vehicle_id} at ({x:.1f}, {y:.1f}, {z:.1f})")

                except struct.error:
                    offset += 1
                    continue

        except Exception as e:
            logger.debug(f"Errore parsing position packet: {e}")

        return positions

    def _search_position_patterns(self, packet_data: bytes, timestamp: float) -> List[Dict]:
        """Cerca pattern di posizione generici nel packet"""
        positions = []

        try:
            # Cerca triple di float che sembrano coordinate
            for i in range(0, len(packet_data) - 11, 1):
                try:
                    # Leggi 3 float consecutivi
                    x, y, z = struct.unpack('<fff', packet_data[i:i+12])

                    # Controlla se sembrano coordinate valide WoT
                    if (abs(x) < 1000 and abs(y) < 1000 and abs(z) < 100 and
                        not (x == 0 and y == 0) and not (abs(x) < 0.1 and abs(y) < 0.1)):

                        # Cerca vehicle ID nei bytes precedenti o successivi
                        vehicle_id = self._find_vehicle_id_around(packet_data, i)

                        if vehicle_id is not None:
                            positions.append({
                                'vehicle_id': vehicle_id,
                                'x': x,
                                'y': y,
                                'z': z,
                                'timestamp': timestamp
                            })

                except struct.error:
                    continue

        except Exception as e:
            logger.debug(f"Errore ricerca pattern: {e}")

        return positions
    
    def _find_vehicle_id_around(self, packet_data: bytes, pos: int) -> Optional[int]:
        """Cerca un vehicle ID valido nei bytes intorno alla posizione"""
        # Cerca negli 16 bytes precedenti e 8 successivi
        search_ranges = [
            (max(0, pos - 16), pos),      # Prima della posizione
            (pos + 12, min(len(packet_data) - 4, pos + 20))  # Dopo la posizione
        ]

        for start, end in search_ranges:
            for i in range(start, end, 4):
                if i + 4 <= len(packet_data):
                    try:
                        potential_id = struct.unpack('<I', packet_data[i:i+4])[0]
                        if potential_id in self.players:
                            return potential_id
                    except struct.error:
                        continue

        return None

    def _extract_battle_events(self, packet_data: bytes, timestamp: float) -> List[Dict]:
        """Estrae eventi di battaglia dai packet (danni, morti, chat, ecc.)"""
        events = []

        try:
            if len(packet_data) < 4:
                return events

            # Chat messages (spesso contengono stringhe)
            if self._contains_text(packet_data):
                text = self._extract_text(packet_data)
                if text:
                    events.append({
                        'type': 'chat',
                        'timestamp': timestamp,
                        'text': text
                    })

            # Damage events (cerca pattern di damage)
            damage_events = self._extract_damage_events(packet_data, timestamp)
            events.extend(damage_events)

        except Exception as e:
            logger.debug(f"Errore estrazione eventi: {e}")

        return events

    def _contains_text(self, data: bytes) -> bool:
        """Controlla se il packet contiene testo leggibile"""
        try:
            text_chars = 0
            for byte in data:
                if 32 <= byte <= 126:  # Caratteri ASCII stampabili
                    text_chars += 1
            return text_chars > len(data) * 0.3  # Almeno 30% caratteri leggibili
        except:
            return False

    def _extract_text(self, data: bytes) -> str:
        """Estrae testo leggibile dal packet"""
        try:
            for encoding in ['utf-8', 'ascii', 'latin-1']:
                try:
                    text = data.decode(encoding, errors='ignore')
                    clean_text = ''.join(c for c in text if c.isprintable())
                    if len(clean_text) > 3:
                        return clean_text[:100]
                except:
                    continue
            return ""
        except:
            return ""

    def _extract_damage_events(self, packet_data: bytes, timestamp: float) -> List[Dict]:
        """Estrae eventi di danno dal packet"""
        events = []

        try:
            for i in range(0, len(packet_data) - 8, 4):
                try:
                    vehicle_id = struct.unpack('<I', packet_data[i:i+4])[0]
                    if vehicle_id in self.players:
                        for j in range(i+4, min(i+20, len(packet_data)-4), 4):
                            damage = struct.unpack('<I', packet_data[j:j+4])[0]
                            if 1 <= damage <= 2000:
                                events.append({
                                    'type': 'damage',
                                    'timestamp': timestamp,
                                    'vehicle_id': vehicle_id,
                                    'damage': damage
                                })
                                break
                except struct.error:
                    continue
        except Exception as e:
            logger.debug(f"Errore estrazione danni: {e}")

        return events

    def _calculate_map_bounds(self):
        """Calcola i bounds della mappa basandosi sulle posizioni"""
        all_x = []
        all_y = []
        
        for movement in self.vehicle_movements.values():
            for pos in movement.positions:
                all_x.append(pos.x)
                all_y.append(pos.y)
        
        if all_x and all_y:
            self.map_bounds = MapBounds(
                min_x=min(all_x) - 50,
                max_x=max(all_x) + 50,
                min_y=min(all_y) - 50,
                max_y=max(all_y) + 50
            )
        else:
            # Genera posizioni simulate per demo
            self._generate_simulated_positions()
    
    def _generate_simulated_positions(self):
        """Genera posizioni simulate per demo quando i dati reali non sono disponibili"""
        logger.info("Generando posizioni simulate per dimostrazione...")
        
        import random
        random.seed(42)  # Per risultati consistenti
        
        # Parametri mappa
        map_size = 1000
        battle_time = 900.0  # 15 minuti
        
        for vehicle_id, movement in self.vehicle_movements.items():
            # Posizione iniziale basata sul team
            if movement.team == 1:
                start_x = random.uniform(-map_size/2, -map_size/4)
            else:
                start_x = random.uniform(map_size/4, map_size/2)
                
            start_y = random.uniform(-map_size/3, map_size/3)
            
            # Genera percorso simulato
            current_x, current_y = start_x, start_y
            
            for t in range(0, int(battle_time), 5):  # Ogni 5 secondi
                # Movimento casuale con bias verso il centro/nemici
                if movement.team == 1:
                    target_x = random.uniform(-100, map_size/3)
                else:
                    target_x = random.uniform(-map_size/3, 100)
                    
                target_y = random.uniform(-200, 200)
                
                # Movimento graduale verso il target
                current_x += (target_x - current_x) * 0.1 + random.uniform(-20, 20)
                current_y += (target_y - current_y) * 0.1 + random.uniform(-20, 20)
                
                # Aggiungi rumore realistico
                current_x += random.uniform(-5, 5)
                current_y += random.uniform(-5, 5)
                
                position = Position(x=current_x, y=current_y, timestamp=t)
                movement.positions.append(position)
        
        self.battle_duration = battle_time
        
        # Aggiorna bounds
        self.map_bounds = MapBounds(
            min_x=-map_size/2, max_x=map_size/2,
            min_y=-map_size/2, max_y=map_size/2
        )
    
    def create_minimap_animation(self, output_file: str = "battle_simulation.gif"):
        """Crea animazione della mini-mappa con effetti avanzati"""
        logger.info("Creando animazione mini-mappa avanzata...")

        # Setup figura con stile più accattivante
        plt.style.use('dark_background')
        fig, ax = plt.subplots(figsize=(14, 12))
        fig.patch.set_facecolor('black')

        ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
        ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
        ax.set_aspect('equal')
        ax.set_facecolor('#1a1a1a')  # Sfondo scuro
        ax.grid(True, alpha=0.2, color='white', linestyle='--')

        # Colori più vivaci per team con gradiente
        team_colors = {1: '#00ff41', 2: '#ff073a', 0: '#0099ff'}  # Verde brillante, Rosso brillante, Blu
        team_colors_light = {1: '#66ff80', 2: '#ff6b85', 0: '#66ccff'}  # Versioni più chiare

        # Crea plot per ogni veicolo con effetti
        vehicle_plots = {}
        explosion_effects = {}  # Per effetti esplosione quando un tank muore

        for vehicle_id, movement in self.vehicle_movements.items():
            color = team_colors.get(movement.team, 'white')
            light_color = team_colors_light.get(movement.team, 'lightgray')

            # Plot principale del tank (più grande e con bordo)
            plot, = ax.plot([], [], 'o', color=color, markersize=12, alpha=0.9,
                           markeredgecolor='white', markeredgewidth=2, zorder=5)

            # Trail con gradiente (scia più lunga)
            trail, = ax.plot([], [], '-', color=color, alpha=0.4, linewidth=3, zorder=2)
            trail_glow, = ax.plot([], [], '-', color=light_color, alpha=0.2, linewidth=6, zorder=1)

            # Effetto pulsante per tank attivi
            pulse, = ax.plot([], [], 'o', color=light_color, markersize=20, alpha=0.0, zorder=3)

            vehicle_plots[vehicle_id] = {
                'plot': plot,
                'trail': trail,
                'trail_glow': trail_glow,
                'pulse': pulse,
                'positions': movement.positions,
                'name': movement.player_name[:10],  # Nome più lungo
                'team': movement.team,
                'last_pos': None,
                'speed': 0.0
            }

            # Inizializza effetti esplosione
            explosion_effects[vehicle_id] = {
                'active': False,
                'frame_count': 0,
                'max_frames': 15
            }

        # Timeline con più frame per animazione fluida
        max_time = max((max([p.timestamp for p in m.positions] if m.positions else [0])
                       for m in self.vehicle_movements.values()), default=0)
        time_steps = np.linspace(0, max_time, 400)  # 400 frame per fluidità
        
        def animate(frame):
            current_time = time_steps[frame] if frame < len(time_steps) else max_time

            ax.clear()
            ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
            ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
            ax.set_aspect('equal')
            ax.set_facecolor('#1a1a1a')
            ax.grid(True, alpha=0.2, color='white', linestyle='--')

            # Titolo con stile migliorato e colori
            battle_time_min = int(current_time // 60)
            battle_time_sec = int(current_time % 60)
            map_name = self.metadata.map_name if self.metadata else "Unknown Map"

            # Titolo con gradiente di colore basato sul tempo
            time_progress = current_time / max_time if max_time > 0 else 0
            title_color = plt.cm.plasma(time_progress)  # Gradiente colore nel tempo

            ax.set_title(f'🎮 {map_name} - Battle Time: {battle_time_min:02d}:{battle_time_sec:02d} ⚔️',
                        fontsize=16, fontweight='bold', color=title_color, pad=20)

            team_counts = {1: 0, 2: 0}
            active_vehicles = []

            for vehicle_id, data in vehicle_plots.items():
                positions = data['positions']
                if not positions:
                    continue

                # Trova posizione corrente e calcola velocità
                current_pos = None
                trail_positions = []
                speed = 0.0

                for i, pos in enumerate(positions):
                    if pos.timestamp <= current_time:
                        trail_positions.append((pos.x, pos.y))
                        current_pos = pos

                        # Calcola velocità basata sulle ultime posizioni
                        if i > 0 and data['last_pos']:
                            dt = pos.timestamp - data['last_pos'].timestamp
                            if dt > 0:
                                dx = pos.x - data['last_pos'].x
                                dy = pos.y - data['last_pos'].y
                                speed = np.sqrt(dx*dx + dy*dy) / dt
                    else:
                        break

                data['last_pos'] = current_pos
                data['speed'] = speed

                if current_pos:
                    color = team_colors.get(data['team'], 'white')
                    light_color = team_colors_light.get(data['team'], 'lightgray')

                    # Trail con effetto glow (più lungo e sfumato)
                    if len(trail_positions) > 1:
                        trail_x, trail_y = zip(*trail_positions[-20:])  # Ultimi 20 punti

                        # Glow effect (alone più largo)
                        ax.plot(trail_x, trail_y, '-', color=light_color, alpha=0.15,
                               linewidth=8, zorder=1, solid_capstyle='round')

                        # Trail principale
                        ax.plot(trail_x, trail_y, '-', color=color, alpha=0.6,
                               linewidth=3, zorder=2, solid_capstyle='round')

                        # Trail interno brillante
                        ax.plot(trail_x, trail_y, '-', color='white', alpha=0.3,
                               linewidth=1, zorder=3)

                    # Effetto pulsante basato sulla velocità
                    pulse_size = 15 + min(speed * 2, 10)  # Dimensione basata su velocità
                    pulse_alpha = 0.3 + min(speed * 0.05, 0.4)

                    # Alone pulsante
                    ax.plot(current_pos.x, current_pos.y, 'o', color=light_color,
                           markersize=pulse_size, alpha=pulse_alpha * np.sin(frame * 0.3),
                           zorder=3)

                    # Tank principale con dimensione variabile
                    tank_size = 10 + min(speed * 0.5, 5)
                    ax.plot(current_pos.x, current_pos.y, 'o', color=color,
                           markersize=tank_size, alpha=0.95,
                           markeredgecolor='white', markeredgewidth=2, zorder=5)

                    # Indicatore direzione (freccia basata su movimento)
                    if len(trail_positions) > 1:
                        last_x, last_y = trail_positions[-2]
                        dx = current_pos.x - last_x
                        dy = current_pos.y - last_y
                        if abs(dx) > 1 or abs(dy) > 1:  # Solo se c'è movimento significativo
                            ax.arrow(current_pos.x, current_pos.y, dx*3, dy*3,
                                   head_width=8, head_length=10, fc=color, ec='white',
                                   alpha=0.7, zorder=4)

                    # Label con nome e velocità
                    speed_text = f"{data['name']}\n{speed:.1f} m/s" if speed > 1 else data['name']
                    ax.annotate(speed_text, (current_pos.x, current_pos.y),
                              xytext=(8, 12), textcoords='offset points',
                              fontsize=9, alpha=0.9, color='white', weight='bold',
                              bbox=dict(boxstyle='round,pad=0.3', facecolor=color,
                                       alpha=0.8, edgecolor='white'))

                    team_counts[data['team']] += 1
                    active_vehicles.append((vehicle_id, current_pos, data['team'], speed))

            # Effetti speciali per battaglia intensa
            if len(active_vehicles) > 5:  # Battaglia intensa
                # Aggiungi particelle di battaglia casuali
                for _ in range(3):
                    x = np.random.uniform(self.map_bounds.min_x, self.map_bounds.max_x)
                    y = np.random.uniform(self.map_bounds.min_y, self.map_bounds.max_y)
                    ax.plot(x, y, '*', color='yellow', markersize=np.random.uniform(3, 8),
                           alpha=np.random.uniform(0.3, 0.7), zorder=6)

            # Legenda migliorata con stile
            legend_elements = []
            for team, color in team_colors.items():
                if team in team_counts and team_counts[team] > 0:
                    team_name = "🟢 Allies" if team == 1 else "🔴 Enemies" if team == 2 else f"Team {team}"
                    legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                                     markerfacecolor=color, markersize=12,
                                                     markeredgecolor='white', markeredgewidth=1,
                                                     label=f'{team_name} ({team_counts[team]})'))

            if legend_elements:
                legend = ax.legend(handles=legend_elements, loc='upper right',
                                 fontsize=11, framealpha=0.9, facecolor='black',
                                 edgecolor='white', title="🏆 Teams")
                legend.get_title().set_color('white')
                legend.get_title().set_fontsize(12)
                for text in legend.get_texts():
                    text.set_color('white')

            # Info battaglia con eventi recenti
            player_name = self.metadata.player_name if self.metadata else 'Unknown'
            player_tank = self.metadata.player_tank if self.metadata else 'Unknown'

            # Trova eventi recenti (ultimi 10 secondi)
            recent_events = [e for e in self.timeline_events
                           if current_time - 10 <= e.get('timestamp', 0) <= current_time]

            info_text = f"🎯 Player: {player_name}\n🚗 Tank: {player_tank}\n"
            info_text += f"⚡ Active: {sum(team_counts.values())}/30\n"
            info_text += f"🕐 Progress: {time_progress*100:.1f}%\n"

            # Aggiungi eventi recenti
            if recent_events:
                info_text += f"📢 Recent Events ({len(recent_events)}):\n"
                for event in recent_events[-3:]:  # Ultimi 3 eventi
                    event_type = event.get('type', 'unknown')
                    if event_type == 'chat':
                        info_text += f"💬 Chat: {event.get('text', '')[:20]}...\n"
                    elif event_type == 'damage':
                        damage = event.get('damage', 0)
                        info_text += f"💥 Damage: {damage}\n"
                    elif event_type == 'death':
                        info_text += f"💀 Tank destroyed\n"

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=10, color='white', weight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='black',
                            alpha=0.8, edgecolor='white'))

            # Barra di progresso battaglia
            progress_width = 0.3
            progress_height = 0.02
            progress_x = 0.35
            progress_y = 0.95

            # Sfondo barra
            ax.add_patch(plt.Rectangle((progress_x, progress_y), progress_width, progress_height,
                                     transform=ax.transAxes, facecolor='gray', alpha=0.5))

            # Barra progresso
            ax.add_patch(plt.Rectangle((progress_x, progress_y), progress_width * time_progress, progress_height,
                                     transform=ax.transAxes, facecolor=title_color, alpha=0.8))

            # Testo progresso
            ax.text(progress_x + progress_width/2, progress_y + progress_height/2,
                   f"Battle Progress: {time_progress*100:.0f}%",
                   transform=ax.transAxes, ha='center', va='center',
                   fontsize=10, color='white', weight='bold')

        # Crea animazione con parametri migliorati
        anim = animation.FuncAnimation(fig, animate, frames=len(time_steps),
                                     interval=50, blit=False, repeat=True)  # 50ms = 20 FPS

        # Salva con qualità migliorata
        try:
            logger.info("Salvando animazione ad alta qualità...")
            anim.save(output_file, writer='pillow', fps=20, dpi=100)
            logger.info(f"✅ Animazione salvata: {output_file}")

            # Mostra anche l'animazione live
            plt.show()

        except Exception as e:
            logger.error(f"❌ Errore salvataggio animazione: {e}")
            logger.info("Mostrando animazione live...")
            plt.show()

        finally:
            plt.style.use('default')  # Ripristina stile default

    def create_live_animation(self):
        """Crea un'animazione live interattiva con effetti speciali"""
        logger.info("🚀 Avviando animazione live interattiva...")

        plt.style.use('dark_background')
        fig, ax = plt.subplots(figsize=(16, 12))
        fig.patch.set_facecolor('black')

        # Setup iniziale
        ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
        ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
        ax.set_aspect('equal')
        ax.set_facecolor('#0a0a0a')

        # Griglia con effetto neon
        ax.grid(True, alpha=0.3, color='cyan', linestyle='-', linewidth=0.5)

        # Colori neon
        neon_colors = {1: '#00ff00', 2: '#ff0040', 0: '#00aaff'}
        glow_colors = {1: '#88ff88', 2: '#ff8888', 0: '#88ccff'}

        max_time = max((max([p.timestamp for p in m.positions] if m.positions else [0])
                       for m in self.vehicle_movements.values()), default=0)

        # Variabili per effetti
        frame_count = 0
        explosion_particles = []

        def update_animation(frame):
            nonlocal frame_count, explosion_particles
            frame_count += 1

            current_time = (frame * max_time / 300) % max_time  # Loop continuo

            ax.clear()
            ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
            ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
            ax.set_aspect('equal')
            ax.set_facecolor('#0a0a0a')

            # Griglia animata
            grid_alpha = 0.2 + 0.1 * np.sin(frame_count * 0.1)
            ax.grid(True, alpha=grid_alpha, color='cyan', linestyle='-', linewidth=0.5)

            # Titolo con effetto pulsante
            battle_min = int(current_time // 60)
            battle_sec = int(current_time % 60)
            map_name = self.metadata.map_name if self.metadata else "🗺️ Battle Arena"

            title_glow = 0.7 + 0.3 * np.sin(frame_count * 0.2)
            ax.set_title(f'⚔️ {map_name} - LIVE BATTLE {battle_min:02d}:{battle_sec:02d} ⚔️',
                        fontsize=18, fontweight='bold', color='white',
                        alpha=title_glow, pad=25)

            # Disegna veicoli con effetti avanzati
            for vehicle_id, movement in self.vehicle_movements.items():
                if not movement.positions:
                    continue

                # Trova posizione corrente
                current_pos = None
                trail_positions = []

                for pos in movement.positions:
                    if pos.timestamp <= current_time:
                        trail_positions.append((pos.x, pos.y))
                        current_pos = pos
                    else:
                        break

                if current_pos:
                    color = neon_colors.get(movement.team, 'white')
                    glow_color = glow_colors.get(movement.team, 'lightgray')

                    # Trail con effetto particelle
                    if len(trail_positions) > 1:
                        trail_x, trail_y = zip(*trail_positions[-15:])

                        # Effetto scia con particelle
                        for i in range(len(trail_x)-1):
                            alpha = (i / len(trail_x)) * 0.8
                            size = 2 + (i / len(trail_x)) * 4
                            ax.plot(trail_x[i], trail_y[i], 'o', color=glow_color,
                                   markersize=size, alpha=alpha, zorder=2)

                    # Tank con effetto pulsante e rotazione
                    pulse = 1.0 + 0.3 * np.sin(frame_count * 0.3 + vehicle_id)

                    # Alone esterno
                    ax.plot(current_pos.x, current_pos.y, 'o', color=glow_color,
                           markersize=25*pulse, alpha=0.3, zorder=3)

                    # Tank principale
                    ax.plot(current_pos.x, current_pos.y, 's', color=color,
                           markersize=12*pulse, alpha=0.9,
                           markeredgecolor='white', markeredgewidth=2, zorder=5)

                    # Effetto scintille casuali
                    if np.random.random() < 0.1:  # 10% probabilità
                        for _ in range(3):
                            spark_x = current_pos.x + np.random.uniform(-15, 15)
                            spark_y = current_pos.y + np.random.uniform(-15, 15)
                            ax.plot(spark_x, spark_y, '*', color='yellow',
                                   markersize=np.random.uniform(2, 6),
                                   alpha=np.random.uniform(0.5, 1.0), zorder=6)

                    # Nome con effetto glow
                    ax.annotate(movement.player_name[:8], (current_pos.x, current_pos.y),
                              xytext=(0, 20), textcoords='offset points', ha='center',
                              fontsize=10, color='white', weight='bold', alpha=0.9,
                              bbox=dict(boxstyle='round,pad=0.3', facecolor=color,
                                       alpha=0.7, edgecolor='white'))

            # Effetti ambientali
            # Stelle di sfondo
            for _ in range(10):
                star_x = np.random.uniform(self.map_bounds.min_x, self.map_bounds.max_x)
                star_y = np.random.uniform(self.map_bounds.min_y, self.map_bounds.max_y)
                ax.plot(star_x, star_y, '.', color='white',
                       markersize=np.random.uniform(1, 3), alpha=0.6, zorder=1)

            # Progress bar animata
            progress = (current_time / max_time) if max_time > 0 else 0
            bar_color = plt.cm.plasma(progress)

            ax.text(0.5, 0.02, f"🎮 Battle Progress: {progress*100:.1f}% 🎮",
                   transform=ax.transAxes, ha='center', va='bottom',
                   fontsize=14, color=bar_color, weight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='black',
                            alpha=0.8, edgecolor=bar_color))

        # Avvia animazione
        anim = animation.FuncAnimation(fig, update_animation, frames=1000,
                                     interval=100, blit=False, repeat=True)

        plt.tight_layout()
        plt.show()

        plt.style.use('default')
        return anim

    def create_static_minimap(self, timestamp: float = None):
        """Crea una mini-mappa statica a un momento specifico"""
        if timestamp is None:
            timestamp = self.battle_duration / 2  # Metà battaglia
            
        fig, ax = plt.subplots(figsize=(12, 10))
        ax.set_xlim(self.map_bounds.min_x, self.map_bounds.max_x)
        ax.set_ylim(self.map_bounds.min_y, self.map_bounds.max_y)
        ax.set_aspect('equal')
        ax.set_facecolor('lightgray')
        ax.grid(True, alpha=0.3)
        
        team_colors = {1: 'green', 2: 'red', 0: 'blue'}
        
        for movement in self.vehicle_movements.values():
            if not movement.positions:
                continue
                
            # Trova posizione al timestamp
            current_pos = None
            trail_x, trail_y = [], []
            
            for pos in movement.positions:
                if pos.timestamp <= timestamp:
                    trail_x.append(pos.x)
                    trail_y.append(pos.y)
                    current_pos = pos
                else:
                    break
            
            if current_pos:
                color = team_colors.get(movement.team, 'black')
                
                # Trail completo
                if len(trail_x) > 1:
                    ax.plot(trail_x, trail_y, '-', color=color, alpha=0.4, linewidth=2)
                
                # Posizione attuale
                ax.plot(current_pos.x, current_pos.y, 'o', color=color, 
                       markersize=12, alpha=0.9, markeredgecolor='white', markeredgewidth=2)
                
                # Nome
                ax.annotate(movement.player_name[:8], (current_pos.x, current_pos.y),
                          xytext=(8, 8), textcoords='offset points', fontsize=9,
                          bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # Titolo e info
        battle_min = int(timestamp // 60)
        battle_sec = int(timestamp % 60)
        map_name = self.metadata.map_name if self.metadata else "Unknown Map"
        
        plt.title(f'{map_name} - Battle Snapshot at {battle_min:02d}:{battle_sec:02d}', 
                 fontsize=16, fontweight='bold')
        
        # Legenda
        legend_elements = []
        for team, color in team_colors.items():
            team_count = len([m for m in self.vehicle_movements.values() if m.team == team])
            if team_count > 0:
                legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                                 markerfacecolor=color, markersize=12,
                                                 label=f'Team {team} ({team_count})'))
        
        plt.legend(handles=legend_elements, loc='upper right', fontsize=12)
        plt.tight_layout()
        plt.show()
    
    def get_battle_statistics(self) -> Dict[str, Any]:
        """Statistiche della battaglia per AI analysis"""
        stats = {
            "duration": self.battle_duration,
            "map": self.metadata.map_name if self.metadata else "unknown",
            "total_vehicles": len(self.vehicle_movements),
            "teams": {},
            "movement_patterns": {},
            "map_usage": self._analyze_map_usage()
        }
        
        # Analisi per team
        for team in [1, 2]:
            team_vehicles = [m for m in self.vehicle_movements.values() if m.team == team]
            if team_vehicles:
                stats["teams"][team] = {
                    "count": len(team_vehicles),
                    "average_positions": sum(len(m.positions) for m in team_vehicles) / len(team_vehicles),
                    "tanks": [m.tank_type for m in team_vehicles]
                }
        
        # Aggiungi statistiche eventi
        stats["events"] = self._analyze_battle_events()

        return stats

    def _analyze_battle_events(self) -> Dict[str, Any]:
        """Analizza gli eventi di battaglia estratti"""
        event_stats = {
            "total_events": len(self.timeline_events),
            "event_types": {},
            "timeline": []
        }

        # Conta eventi per tipo
        for event in self.timeline_events:
            event_type = event.get('type', 'unknown')
            event_stats["event_types"][event_type] = event_stats["event_types"].get(event_type, 0) + 1

        # Crea timeline degli eventi (raggruppati per minuto)
        events_by_minute = {}
        for event in self.timeline_events:
            minute = int(event.get('timestamp', 0) // 60)
            if minute not in events_by_minute:
                events_by_minute[minute] = []
            events_by_minute[minute].append(event)

        for minute in sorted(events_by_minute.keys()):
            event_stats["timeline"].append({
                "minute": minute,
                "events": len(events_by_minute[minute]),
                "types": list(set(e.get('type', 'unknown') for e in events_by_minute[minute]))
            })

        return event_stats

    def print_battle_summary(self):
        """Stampa un riassunto dettagliato della battaglia"""
        print("\n" + "="*60)
        print("🎮 WORLD OF TANKS BATTLE ANALYSIS 🎮")
        print("="*60)

        # Info generali
        map_name = self.metadata.map_name if self.metadata else "Unknown Map"
        player_name = self.metadata.player_name if self.metadata else "Unknown Player"
        player_tank = self.metadata.player_tank if self.metadata else "Unknown Tank"

        print(f"🗺️  Map: {map_name}")
        print(f"🎯 Player: {player_name}")
        print(f"🚗 Tank: {player_tank}")
        print(f"⏱️  Duration: {self.battle_duration/60:.1f} minutes")
        print(f"👥 Total Vehicles: {len(self.vehicle_movements)}")

        # Statistiche posizioni
        total_positions = sum(len(m.positions) for m in self.vehicle_movements.values())
        print(f"📍 Total Positions Extracted: {total_positions}")

        # Statistiche eventi
        print(f"📢 Total Events Extracted: {len(self.timeline_events)}")

        event_types = {}
        for event in self.timeline_events:
            event_type = event.get('type', 'unknown')
            event_types[event_type] = event_types.get(event_type, 0) + 1

        if event_types:
            print("📊 Event Types:")
            for event_type, count in sorted(event_types.items()):
                emoji = {"chat": "💬", "damage": "💥", "death": "💀"}.get(event_type, "📝")
                print(f"   {emoji} {event_type.title()}: {count}")

        # Team info
        team_stats = {}
        for movement in self.vehicle_movements.values():
            team = movement.team
            if team not in team_stats:
                team_stats[team] = {"count": 0, "avg_positions": 0}
            team_stats[team]["count"] += 1
            team_stats[team]["avg_positions"] += len(movement.positions)

        print("\n🏆 Team Statistics:")
        for team, stats in team_stats.items():
            avg_pos = stats["avg_positions"] / stats["count"] if stats["count"] > 0 else 0
            team_name = "🟢 Allies" if team == 1 else "🔴 Enemies" if team == 2 else f"Team {team}"
            print(f"   {team_name}: {stats['count']} tanks, {avg_pos:.1f} avg positions")

        print("="*60)
    
    def _analyze_map_usage(self) -> Dict[str, Any]:
        """Analizza l'utilizzo della mappa"""
        # Dividi mappa in settori
        sectors = {"north": 0, "south": 0, "east": 0, "west": 0, "center": 0}
        
        center_x = (self.map_bounds.min_x + self.map_bounds.max_x) / 2
        center_y = (self.map_bounds.min_y + self.map_bounds.max_y) / 2
        
        for movement in self.vehicle_movements.values():
            for pos in movement.positions:
                if pos.y > center_y + 100:
                    sectors["north"] += 1
                elif pos.y < center_y - 100:
                    sectors["south"] += 1
                elif pos.x > center_x + 100:
                    sectors["east"] += 1
                elif pos.x < center_x - 100:
                    sectors["west"] += 1
                else:
                    sectors["center"] += 1
        
        return sectors

# Esempio di utilizzo
if __name__ == "__main__":
    replay_file = "example.wotreplay"
    
    # Crea simulatore
    simulator = BattleSimulator(replay_file)
    
    if simulator.simulate_battle():
        print("=== SIMULAZIONE BATTAGLIA ===")
        stats = simulator.get_battle_statistics()
        print(json.dumps(stats, indent=2))
        
        print("\n🎮 Creando visualizzazioni mini-mappa avanzate...")

        # Chiedi all'utente quale visualizzazione preferisce
        print("\nScegli il tipo di visualizzazione:")
        print("1. 🎬 Animazione Live Interattiva (Consigliata)")
        print("2. 💾 Animazione GIF da salvare")
        print("3. 📸 Mappa statica")
        print("4. 🚀 Tutte le visualizzazioni")

        try:
            choice = input("\nInserisci la tua scelta (1-4) [default: 1]: ").strip()
            if not choice:
                choice = "1"
        except:
            choice = "1"

        if choice == "1" or choice == "4":
            print("🚀 Avviando animazione live...")
            try:
                simulator.create_live_animation()
            except Exception as e:
                print(f"❌ Errore animazione live: {e}")

        if choice == "2" or choice == "4":
            print("💾 Creando animazione GIF...")
            try:
                simulator.create_minimap_animation("battle_animation_enhanced.gif")
                print("✅ Animazione GIF creata: battle_animation_enhanced.gif")
            except Exception as e:
                print(f"❌ Errore creazione GIF: {e}")

        if choice == "3" or choice == "4":
            print("📸 Creando mappa statica...")
            try:
                simulator.create_static_minimap()
            except Exception as e:
                print(f"❌ Errore mappa statica: {e}")

        print("\n🎯 Visualizzazione completata!")
    
    else:
        print("Errore nella simulazione battaglia")